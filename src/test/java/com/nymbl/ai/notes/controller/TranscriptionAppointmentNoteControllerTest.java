package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.ReviewedNoteRequest;
import com.nymbl.ai.notes.dto.TranscriptionAppointmentNotesDto;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class TranscriptionAppointmentNoteControllerTest {

    @Mock
    private TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    @InjectMocks
    private TranscriptionAppointmentNoteController transcriptionNotesController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSearchWithResults() {
        // Prepare test data
        List<Long> practitionerIds = List.of(1L, 2L);
        Long patientId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        Long appointmentId = 1L;
        String status = "COMPLETED";
        Pageable pageable = PageRequest.of(0, 1000);

        // Mock the service method
        List<TranscriptionDetailsDto> mockResults = List.of(new TranscriptionDetailsDto());
        when(transcriptionAppointmentNoteService.search(practitionerIds, patientId, appointmentId, startDate, endDate, status, pageable))
                .thenReturn(Optional.of(mockResults));

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.search(practitionerIds, patientId, startDate, endDate, appointmentId, status, pageable, mock(HttpServletRequest.class));

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResults), response);
        verify(transcriptionAppointmentNoteService, times(1)).search(practitionerIds, patientId, appointmentId, startDate, endDate, status, pageable);
    }

    @Test
    void testSearchWithoutResults() {
        // Prepare test data
        List<Long> practitionerIds = List.of(1L, 2L);
        Long patientId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        Long appointmentId = 1L;
        String status = "COMPLETED";
        Pageable pageable = PageRequest.of(0, 1000);

        // Mock the service method
        when(transcriptionAppointmentNoteService.search(practitionerIds, patientId, appointmentId, startDate, endDate, status, pageable))
                .thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.search(practitionerIds, patientId, startDate, endDate, appointmentId, status, pageable, mock(HttpServletRequest.class));

        // Verify the response
        assertEquals(ResponseEntity.noContent().build(), response);
        verify(transcriptionAppointmentNoteService, times(1)).search(practitionerIds, patientId, appointmentId, startDate, endDate, status, pageable);
    }

    @Test
    void testViewWithResults() {
        // Prepare test data
        Long transcriptionAppointmentNotesId = 1L;
        TranscriptionAppointmentNotesDto mockResult = new TranscriptionAppointmentNotesDto();

        // Mock the service method
        when(transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionAppointmentNotesId)).thenReturn(Optional.of(mockResult));

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.view(transcriptionAppointmentNotesId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResult), response);
        verify(transcriptionAppointmentNoteService, times(1)).viewGeneratedNotes(transcriptionAppointmentNotesId);
    }

    @Test
    void testViewWithoutResults() {
        // Prepare test data
        Long transcriptionAppointmentNotesId = 1L;

        // Mock the service method
        when(transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionAppointmentNotesId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.view(transcriptionAppointmentNotesId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().build(), response);
        verify(transcriptionAppointmentNoteService, times(1)).viewGeneratedNotes(transcriptionAppointmentNotesId);
    }

    @Test
    void testDetailsWithResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;
        TranscriptionDetailsDto mockResult = new TranscriptionDetailsDto();

        // Mock the service method
        when(transcriptionAppointmentNoteService.getDetailsById(transcriptionDetailsId)).thenReturn(Optional.of(mockResult));

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.details(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResult), response);
        verify(transcriptionAppointmentNoteService, times(1)).getDetailsById(transcriptionDetailsId);
    }

    @Test
    void testDetailsWithoutResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;

        // Mock the service method
        when(transcriptionAppointmentNoteService.getDetailsById(transcriptionDetailsId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.details(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().build(), response);
        verify(transcriptionAppointmentNoteService, times(1)).getDetailsById(transcriptionDetailsId);
    }

    @Test
    void testNotesWithResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;
        TranscriptionAppointmentNotesDto mockResult = new TranscriptionAppointmentNotesDto();
        String mockClinicalNotesString = "Mock clinical notes";

        // Mock the service methods
        when(transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId)).thenReturn(Optional.of(mockResult));
        when(transcriptionAppointmentNoteService.getClinicalNotesString(any())).thenReturn(mockClinicalNotesString);

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.notes(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockClinicalNotesString), response);
        verify(transcriptionAppointmentNoteService, times(1)).viewGeneratedNotes(transcriptionDetailsId);
        verify(transcriptionAppointmentNoteService, times(1)).getClinicalNotesString(any());
    }

    @Test
    void testNotesWithoutResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;

        // Mock the service method
        when(transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.notes(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().build(), response);
        verify(transcriptionAppointmentNoteService, times(1)).viewGeneratedNotes(transcriptionDetailsId);
    }

    @Test
    void testTranscriptWithResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;
        TranscriptionAppointmentNotesDto mockResult = new TranscriptionAppointmentNotesDto();
        String mockTranscriptsString = "Mock transcripts";

        // Mock the service methods
        when(transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId)).thenReturn(Optional.of(mockResult));
        when(transcriptionAppointmentNoteService.getTranscriptsString(any())).thenReturn(mockTranscriptsString);

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.transcript(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockTranscriptsString), response);
        verify(transcriptionAppointmentNoteService, times(1)).viewGeneratedNotes(transcriptionDetailsId);
        verify(transcriptionAppointmentNoteService, times(1)).getTranscriptsString(any());
    }

    @Test
    void testTranscriptWithoutResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;

        // Mock the service method
        when(transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.transcript(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().build(), response);
        verify(transcriptionAppointmentNoteService, times(1)).viewGeneratedNotes(transcriptionDetailsId);
    }

    @Test
    void testSaveReviewedNotesSuccess() {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();

        // Mock the service method
        when(transcriptionAppointmentNoteService.reviewNotes(request)).thenReturn("success");

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.saveReviewedNotes(request);

        // Verify the response
        assertEquals(ResponseEntity.ok("success"), response);
        verify(transcriptionAppointmentNoteService, times(1)).reviewNotes(request);
    }

    @Test
    void testSaveReviewedNotesFailure() {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();

        // Mock the service method
        when(transcriptionAppointmentNoteService.reviewNotes(request)).thenReturn("failure");

        // Call the controller method
        ResponseEntity<?> response = transcriptionNotesController.saveReviewedNotes(request);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("failure"), response);
        verify(transcriptionAppointmentNoteService, times(1)).reviewNotes(request);
    }
}
