package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.service.HealthScribeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class HealthScribeControllerTest {

    @Mock
    private HealthScribeService healthScribeService;

    @InjectMocks
    private HealthScribeController healthScribeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUploadAudioSuccess() {
        // Prepare test data
        MultipartFile file = new MockMultipartFile("file", "audio.m4a", MediaType.MULTIPART_FORM_DATA_VALUE, "audio content".getBytes());
        Long practitionerId = 1L;
        Long appointmentId = 2L;
        Long patientId = 3L;
        Long prescriptionId = 4L;
        Long branchId = 5L;
        Long detailsId = 100L;

        // Mock the service methods
        when(healthScribeService.initiateRecording(any(TranscriptionUploadRequest.class))).thenReturn(detailsId);
        when(healthScribeService.uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId))).thenReturn("success");

        // Call the controller method
        ResponseEntity<?> response = healthScribeController.uploadAudio(file, practitionerId, appointmentId, patientId, prescriptionId, branchId, "record", 0);

        // Verify the response
        assertEquals(ResponseEntity.ok("success"), response);
        verify(healthScribeService, times(1)).initiateRecording(any(TranscriptionUploadRequest.class));
        verify(healthScribeService, times(1)).uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId));
    }

    @Test
    void testUploadAudioFailure() {
        // Prepare test data
        MultipartFile file = new MockMultipartFile("file", "audio.m4a", MediaType.MULTIPART_FORM_DATA_VALUE, "audio content".getBytes());
        Long practitionerId = 1L;
        Long appointmentId = 2L;
        Long patientId = 3L;
        Long prescriptionId = 4L;
        Long branchId = 5L;
        Long detailsId = 100L;

        // Mock the service methods
        when(healthScribeService.initiateRecording(any(TranscriptionUploadRequest.class))).thenReturn(detailsId);
        when(healthScribeService.uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId))).thenReturn("Exception");

        // Call the controller method
        ResponseEntity<?> response = healthScribeController.uploadAudio(file, practitionerId, appointmentId, patientId, prescriptionId, branchId, "record", 0);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("Exception"), response);
        verify(healthScribeService, times(1)).initiateRecording(any(TranscriptionUploadRequest.class));
        verify(healthScribeService, times(1)).uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId));
    }
}

