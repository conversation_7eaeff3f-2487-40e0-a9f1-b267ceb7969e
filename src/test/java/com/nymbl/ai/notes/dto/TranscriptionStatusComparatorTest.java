package com.nymbl.ai.notes.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class TranscriptionStatusComparatorTest {
    
    private TranscriptionStatusComparator comparator;
    private TranscriptionDetailsDto readyDto;
    private TranscriptionDetailsDto startedDto;
    private TranscriptionDetailsDto generatingDto;
    private TranscriptionDetailsDto publishedDto;

    @BeforeEach
    void setUp() {
        comparator = new TranscriptionStatusComparator();
        
        readyDto = new TranscriptionDetailsDto();
        readyDto.setStatus(AINOTE.READY);
        
        startedDto = new TranscriptionDetailsDto();
        startedDto.setStatus(AINOTE.STARTED);
        
        generatingDto = new TranscriptionDetailsDto();
        generatingDto.setStatus(AINOTE.GENERATING);

        publishedDto = new TranscriptionDetailsDto();
        publishedDto.setStatus(AINOTE.PUBLISHED);
    }

    @Test
    void testReadyComesBeforeOtherStatuses() {
        // READY vs STARTED
        assertEquals(-1, comparator.compare(readyDto, startedDto));
        assertEquals(1, comparator.compare(startedDto, readyDto));
        
        // READY vs GENERATING
        assertEquals(-1, comparator.compare(readyDto, generatingDto));
        assertEquals(1, comparator.compare(generatingDto, readyDto));

        // READY vs PUBLISHED
        assertEquals(-1, comparator.compare(readyDto, publishedDto));
        assertEquals(1, comparator.compare(publishedDto, readyDto));
    }

    @Test
    void testSameStatusReturnsZero() {
        assertEquals(0, comparator.compare(readyDto, readyDto));
        assertEquals(0, comparator.compare(startedDto, startedDto));
        assertEquals(0, comparator.compare(generatingDto, generatingDto));
        assertEquals(0, comparator.compare(publishedDto, publishedDto));
    }

    @Test
    void testNonReadyStatusesPreserveOrder() {
        assertEquals(0, comparator.compare(startedDto, generatingDto));
        assertEquals(0, comparator.compare(generatingDto, publishedDto));
        assertEquals(0, comparator.compare(startedDto, publishedDto));
    }

    @Test
    void testNullHandling() {
        assertEquals(0, comparator.compare(null, readyDto));
        assertEquals(0, comparator.compare(readyDto, null));
        assertEquals(0, comparator.compare(null, null));
    }
} 