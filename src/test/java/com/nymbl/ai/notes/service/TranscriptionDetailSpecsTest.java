package com.nymbl.ai.notes.service;

import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.tenant.model.Appointment;
import jakarta.persistence.criteria.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.jpa.domain.Specification;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

class TranscriptionDetailSpecsTest {

    @Mock
    private CriteriaBuilder criteriaBuilder;

    @Mock
    private CriteriaQuery<?> criteriaQuery;

    @Mock
    private Root<TranscriptionDetail> root;

    @Mock
    private Join<TranscriptionDetail, Appointment> appointmentJoin;

    @Mock
    private Path<Long> patientIdPath;

    @Mock
    private Path<Long> practitionerIdPath;

    @Mock
    private Path<Long> appointmentIdPath;

    @Mock
    private Path<String> stringPath;

    @Mock
    private Path<Timestamp> timestampPath;

    @Mock
    private CriteriaBuilder.In<Long> inPredicate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Mock return types for root.get(...) with proper generics
        when(root.<Long>get("patientId")).thenReturn(patientIdPath);
        when(root.<Long>get("practitionerId")).thenReturn(practitionerIdPath);
        when(root.<Long>get("appointmentId")).thenReturn(appointmentIdPath);
        when(root.<String>get("status")).thenReturn(stringPath);

        // Mock join
        when(root.<TranscriptionDetail, Appointment>join("appointment", JoinType.INNER)).thenReturn(appointmentJoin);

        // Mock return types for join.get(...) with proper generics
        when(appointmentJoin.<Timestamp>get("startDateTime")).thenReturn(timestampPath);
        when(appointmentJoin.<Timestamp>get("endDateTime")).thenReturn(timestampPath);

        // Mock CriteriaBuilder methods
        when(criteriaBuilder.equal(any(), any())).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.in(any(Path.class))).thenReturn(inPredicate);
        when(inPredicate.value((Long) any())).thenReturn(inPredicate);
        when(criteriaBuilder.and(any(Predicate.class))).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.and(any(Predicate[].class))).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.greaterThanOrEqualTo(any(), any(Timestamp.class))).thenReturn(mock(Predicate.class));
        when(criteriaBuilder.lessThanOrEqualTo(any(), any(Timestamp.class))).thenReturn(mock(Predicate.class));
    }

    @Test
    void testSearchWithAllParameters() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";

        Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(practitionerId, patientId, appointmentId, startDate, endDate, status);
        Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

        assertNotNull(predicate);
        verify(criteriaBuilder, times(1)).equal(root.get("patientId"), patientId);
        verify(root, times(1)).get("practitionerId");
        verify(criteriaBuilder, times(1)).in(root.get("practitionerId"));
        verify(inPredicate, times(1)).value(1L);
        verify(inPredicate, times(1)).value(2L);
        verify(criteriaBuilder, times(1)).equal(root.get("appointmentId"), appointmentId);
        verify(criteriaBuilder, times(1)).greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), new Timestamp(startDate.getTime()));
        verify(criteriaBuilder, times(1)).lessThanOrEqualTo(appointmentJoin.get("endDateTime"), new Timestamp(endDate.getTime()));
        verify(criteriaBuilder, times(1)).equal(root.get("status"), status);
    }

    @Test
    void testSearchWithNoAppointmentId() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";

        Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(practitionerId, patientId, null, startDate, endDate, status);
        Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

        assertNotNull(predicate);
        verify(criteriaBuilder, times(1)).equal(root.get("patientId"), patientId);
        verify(root, times(1)).get("practitionerId");
        verify(criteriaBuilder, times(1)).in(root.get("practitionerId"));
        verify(inPredicate, times(1)).value(1L);
        verify(inPredicate, times(1)).value(2L);
        verify(criteriaBuilder, times(0)).equal(root.get("appointmentId"), null);
        verify(criteriaBuilder, times(1)).greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), new Timestamp(startDate.getTime()));
        verify(criteriaBuilder, times(1)).lessThanOrEqualTo(appointmentJoin.get("endDateTime"), new Timestamp(endDate.getTime()));
        verify(criteriaBuilder, times(1)).equal(root.get("status"), status);
    }

    @Test
    void testSearchWithNoStartDateAndEndDate() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        String status = "completed";

        Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(practitionerId, patientId, appointmentId, null, null, status);
        Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

        assertNotNull(predicate);
        verify(criteriaBuilder, times(1)).equal(root.get("patientId"), patientId);
        verify(root, times(1)).get("practitionerId");
        verify(criteriaBuilder, times(1)).in(root.get("practitionerId"));
        verify(inPredicate, times(1)).value(1L);
        verify(inPredicate, times(1)).value(2L);
        verify(criteriaBuilder, times(1)).equal(root.get("appointmentId"), appointmentId);
        verify(criteriaBuilder, times(0)).greaterThanOrEqualTo(any(), any(Timestamp.class));
        verify(criteriaBuilder, times(0)).lessThanOrEqualTo(any(), any(Timestamp.class));
        verify(criteriaBuilder, times(1)).equal(root.get("status"), status);
    }

    @Test
    void testSearchWithNoStatus() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();

        Specification<TranscriptionDetail> spec = TranscriptionDetailSpecs.search(practitionerId, patientId, appointmentId, startDate, endDate, null);
        Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);

        assertNotNull(predicate);
        verify(criteriaBuilder, times(1)).equal(root.get("patientId"), patientId);
        verify(root, times(1)).get("practitionerId");
        verify(criteriaBuilder, times(1)).in(root.get("practitionerId"));
        verify(inPredicate, times(1)).value(1L);
        verify(inPredicate, times(1)).value(2L);
        verify(criteriaBuilder, times(1)).equal(root.get("appointmentId"), appointmentId);
        verify(criteriaBuilder, times(1)).greaterThanOrEqualTo(appointmentJoin.get("startDateTime"), new Timestamp(startDate.getTime()));
        verify(criteriaBuilder, times(1)).lessThanOrEqualTo(appointmentJoin.get("endDateTime"), new Timestamp(endDate.getTime()));
        verify(criteriaBuilder, times(0)).equal(root.get("status"), null);
    }
}
