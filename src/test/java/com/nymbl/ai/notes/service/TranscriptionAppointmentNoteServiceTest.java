package com.nymbl.ai.notes.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentationContainer;
import com.nymbl.ai.notes.data.clinicaldoc.Section;
import com.nymbl.ai.notes.data.conversation.Conversation;
import com.nymbl.ai.notes.data.conversation.ConversationContainer;
import com.nymbl.ai.notes.data.conversation.TranscriptSegment;
import com.nymbl.ai.notes.dto.AiNoteWebhookDto;
import com.nymbl.ai.notes.dto.ReviewedNoteRequest;
import com.nymbl.ai.notes.dto.TranscriptionAppointmentNotesDto;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionAppointmentNoteRepository;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.service.NoteService;
import com.nymbl.tenant.service.NotificationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class TranscriptionAppointmentNoteServiceTest {

    @Mock
    private TranscriptionDetailRepository transcriptionDetailRepository;

    @Mock
    private TranscriptionAppointmentNoteRepository transcriptionAppointmentNoteRepository;

    @Mock
    private TranscriptionUtil transcriptionUtil;

    @Mock
    private NotificationService notificationService;

    @Mock
    private NoteService noteService;

    @Mock
    private AwsUtil awsUtil;

    @Mock
    private AWSS3Service awsS3Service;

    private TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        transcriptionAppointmentNoteService = new TranscriptionAppointmentNoteService(
                transcriptionDetailRepository,
                transcriptionAppointmentNoteRepository,
                transcriptionUtil,
                notificationService,
                noteService,
                awsUtil
        );
    }

    @Test
    void testSearch() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";
        Pageable pageable = mock(Pageable.class);
        Page<TranscriptionDetail> page = mock(Page.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        when(transcriptionDetailRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(page.getContent()).thenReturn(Collections.singletonList(transcriptionDetail));
        when(transcriptionUtil.convertToDto(transcriptionDetail)).thenReturn(mock(TranscriptionDetailsDto.class));

        Optional<List<TranscriptionDetailsDto>> result = transcriptionAppointmentNoteService.search(practitionerId, patientId, appointmentId, startDate, endDate, status, pageable);

        assertTrue(result.isPresent());
        verify(transcriptionDetailRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verify(transcriptionUtil, times(1)).convertToDto(transcriptionDetail);
    }

    @Test
    void testGetDetailsById() {
        Long detailsId = 1L;
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        when(transcriptionDetailRepository.findById(detailsId)).thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionUtil.convertToDto(transcriptionDetail)).thenReturn(mock(TranscriptionDetailsDto.class));

        Optional<TranscriptionDetailsDto> result = transcriptionAppointmentNoteService.getDetailsById(detailsId);

        assertTrue(result.isPresent());
        verify(transcriptionDetailRepository, times(1)).findById(detailsId);
        verify(transcriptionUtil, times(1)).convertToDto(transcriptionDetail);
    }

    @Test
    void testWriteCompletedNotesFromBucket() {
        AiNoteWebhookDto aiNoteWebhookDto = mock(AiNoteWebhookDto.class);
        byte[] summaryBytes = "summary".getBytes(StandardCharsets.UTF_8);
        byte[] transcriptBytes = "transcript".getBytes(StandardCharsets.UTF_8);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        String jobName = "123456";
        Long detailId = 123456L;

        when(aiNoteWebhookDto.getBucketName()).thenReturn("bucketName");
        when(aiNoteWebhookDto.getEventTime()).thenReturn("2034-02-25T00:00:00.000Z");
        when(aiNoteWebhookDto.getJobName()).thenReturn(jobName);
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.of(summaryBytes), Optional.of(transcriptBytes));
        when(transcriptionUtil.removeLeadingZeroes(jobName)).thenReturn(detailId);
        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(detailId)).thenReturn(null);
        when(transcriptionDetailRepository.findById(detailId)).thenReturn(Optional.of(transcriptionDetail));

        boolean result = transcriptionAppointmentNoteService.writeCompletedNotesFromBucket(aiNoteWebhookDto);

        assertTrue(result);
        verify(awsS3Service, times(1)).getFile(aiNoteWebhookDto.getBucketName(), jobName + "/summary.json");
        verify(awsS3Service, times(1)).getFile(aiNoteWebhookDto.getBucketName(), jobName + "/transcript.json");
        verify(transcriptionDetailRepository, times(1)).findById(detailId);
        verify(transcriptionAppointmentNoteRepository, times(1)).save(any(TranscriptionAppointmentNote.class));
        verify(transcriptionDetailRepository, times(1)).save(any(TranscriptionDetail.class));
        verify(notificationService, times(1)).createAINoteNotification(transcriptionDetail);
    }

    @Test
    void testViewGeneratedNotes() throws JsonProcessingException {
        Long transcriptionDetailsId = 1L;

        ConversationContainer conversation = new ConversationContainer();
        Conversation conversation1 = mock(Conversation.class);
        conversation.setConversation(conversation1);

        TranscriptionAppointmentNote transcriptionAppointmentNote = new TranscriptionAppointmentNote();
        transcriptionAppointmentNote.setGeneratedConversation("conversation");
        transcriptionAppointmentNote.setGenerateClinicalNotes("notes");

        ClinicalDocumentationContainer clinicalDocumentation = new ClinicalDocumentationContainer();
        ClinicalDocumentation documentation = mock(ClinicalDocumentation.class);
        clinicalDocumentation.setClinicalDocumentation(documentation);
        Section section = mock(Section.class);
        List<Section> sections = Arrays.asList(section, section);

        when(documentation.getSections()).thenReturn(sections);
        when(section.printSectionDetails()).thenReturn("details");


        TranscriptSegment segment = mock(TranscriptSegment.class);
        List<TranscriptSegment> segments = Arrays.asList(segment, segment);

        when(conversation1.getTranscriptSegments()).thenReturn(segments);
        when(segment.printTranscriptSegment()).thenReturn("segment");


        when(transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId)).thenReturn(transcriptionAppointmentNote);
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class))).thenReturn(conversation);
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class))).thenReturn(clinicalDocumentation);

        Optional<TranscriptionAppointmentNotesDto> result = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);

        assertTrue(result.isPresent());
        verify(transcriptionAppointmentNoteRepository, times(1)).findByTranscriptionDetailId(transcriptionDetailsId);
        verify(transcriptionUtil, times(1)).fromJson(anyString(), eq(ConversationContainer.class));
        verify(transcriptionUtil, times(1)).fromJson(anyString(), eq(ClinicalDocumentationContainer.class));
    }

    @Test
    void testReviewNotes() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionAppointmentNoteId(1L);
        request.setAction("done");
        TranscriptionAppointmentNote transcriptionAppointmentNote = mock(TranscriptionAppointmentNote.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        Note note = new Note();
        note.setId(123L);  // Ensure this note has a valid ID
        Map<String, Object> noteMap = new HashMap<>();
        noteMap.put(OptimisticLockingUtil.SAVED, note);

        when(transcriptionAppointmentNoteRepository.findById(anyLong())).thenReturn(Optional.of(transcriptionAppointmentNote));
        when(transcriptionDetailRepository.findById(anyLong())).thenReturn(Optional.of(transcriptionDetail));
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        String result = transcriptionAppointmentNoteService.reviewNotes(request);

        assertEquals("success", result);
        verify(transcriptionAppointmentNoteRepository, times(1)).findById(anyLong());
        verify(transcriptionDetailRepository, times(1)).findById(anyLong());
        verify(noteService, times(1)).saveNote(any(Note.class), anyString());
        verify(transcriptionAppointmentNoteRepository, times(1)).save(any(TranscriptionAppointmentNote.class));
    }

    @Test
    void testGetClinicalNotesString() {
        ClinicalDocumentation clinicalDocumentation = mock(ClinicalDocumentation.class);
        Section section = mock(Section.class);
        List<Section> sections = Arrays.asList(section, section);

        when(clinicalDocumentation.getSections()).thenReturn(sections);
        when(section.printSectionDetails()).thenReturn("details");

        String result = transcriptionAppointmentNoteService.getClinicalNotesString(clinicalDocumentation);

        assertTrue(result.contains("details"));
        assertTrue(result.contains("&lt;br /&gt;"));
    }

    @Test
    void testGetTranscriptsString() {
        Conversation conversation = mock(Conversation.class);
        TranscriptSegment segment = mock(TranscriptSegment.class);
        List<TranscriptSegment> segments = Arrays.asList(segment, segment);

        when(conversation.getTranscriptSegments()).thenReturn(segments);
        when(segment.printTranscriptSegment()).thenReturn("segment");

        String result = transcriptionAppointmentNoteService.getTranscriptsString(conversation);

        assertTrue(result.contains("segment"));
    }
}
