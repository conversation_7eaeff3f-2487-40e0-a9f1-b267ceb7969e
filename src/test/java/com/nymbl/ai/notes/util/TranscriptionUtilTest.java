package com.nymbl.ai.notes.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.UserDto;
import com.nymbl.master.repository.UserDtoRepository;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.dashboard.dto.AppointmentDto;
import com.nymbl.tenant.dashboard.repository.AppointmentDtoRepository;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.Patient;
import com.nymbl.tenant.repository.BranchRepository;
import com.nymbl.tenant.repository.PatientRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TranscriptionUtilTest {

    @Mock
    private BranchRepository branchRepository;

    @Mock
    private PatientRepository patientRepository;

    @Mock
    private AppointmentDtoRepository appointmentRepository;

    @Mock
    private UserDtoRepository userRepository;

    @Mock
    private StringUtil stringUtil;

    @InjectMocks
    private TranscriptionUtil transcriptionUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testReplacePathVariables() {
        String pathTemplate = "/branch/{branch}/patient/{patientId}";
        Map<String, String> values = new HashMap<>();
        values.put("branch", "branch1");
        values.put("patientId", "123");

        String result = transcriptionUtil.replacePathVariables(pathTemplate, values);

        assertEquals("/branch/branch1/patient/123", result);
    }

    @Test
    public void testConvertToMap() {
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setBranchId(1L);
        request.setPatientId(2L);
        request.setPractitionerId(3L);

        Branch branch = new Branch();
        branch.setName("BranchName");
        when(branchRepository.findById(1L)).thenReturn(Optional.of(branch));
        TenantContext.setCurrentTenant("tenant1");

        Map<String, String> result = transcriptionUtil.convertToMap(request);

        assertEquals("BranchName", result.get("branch"));
        assertEquals("2", result.get("patientId"));
        assertEquals("3", result.get("practitionerId"));
        assertEquals("tenant1", result.get("tenant"));
    }

    @Test
    public void testFromJson() throws JsonProcessingException {
        String json = "{\"id\":1,\"name\":\"test\"}";
        TestObject expectedObject = new TestObject(1, "test");

        TestObject result = stringUtil.fromJson(json, TestObject.class);

        assertEquals(expectedObject, result);
    }

    @Test
    public void testToJson() throws JsonProcessingException {
        TestObject testObject = new TestObject(1, "test");

        String result = stringUtil.toJson(testObject);

        assertEquals("{\"id\":1,\"name\":\"test\"}", result);
    }

    @Test
    public void testMakeJobName() {
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setId(1L);
        TenantContext.setCurrentTenant("tenant1");

        String result = transcriptionUtil.makeJobName(transcriptionDetail);

        assertEquals("TStenant1-1", result);
    }

    @Test
    public void testRemoveLeadingZeroes() {
        String input = "TS-001";

        Long result = transcriptionUtil.removeLeadingZeroes(input);

        assertEquals(Long.valueOf(1), result);
    }

    @Test
    public void testConvertToDto() {
        TranscriptionDetail details = new TranscriptionDetail();
        details.setId(1L);
        details.setStatus(AINOTE.STARTED);
        details.setJobName("jobName");
        details.setBranchId(1L);
        details.setPrescriptionId(1L);
        details.setStartTime(new Timestamp(new Date().getTime()));
        details.setEndTime(new Timestamp(new Date().getTime()));
        details.setPatientId(2L);
        details.setPractitionerId(3L);
        Appointment appointment = new Appointment();
        appointment.setId(1L);
        details.setAppointmentId(1L);
        details.setAppointment(appointment);

        Patient patient = new Patient();
        patient.setId(2L);
        patient.setFirstName("firstName");
        patient.setLastName("lastName");
        when(patientRepository.findById(2L)).thenReturn(Optional.of(patient));

        AppointmentDto appointmentDto = mock(AppointmentDto.class);
        when(appointmentDto.getId()).thenReturn(1L);
        when(appointmentRepository.getAppointmentDtoById(1L)).thenReturn(appointmentDto);

        UserDto practitioner = mock(UserDto.class);
        when(practitioner.getId()).thenReturn(3L);
        when(userRepository.findUserDtoById(3L)).thenReturn(practitioner);

        TranscriptionDetailsDto result = transcriptionUtil.convertToDto(details);

        assertNotNull(result);
        assertEquals(details.getId(), result.getId());
        assertEquals(details.getStatus(), result.getStatus());
        assertEquals(details.getJobName(), result.getJobName());
        assertEquals(details.getBranchId(), result.getBranchId());
        assertEquals(details.getPrescriptionId(), result.getPrescriptionId());
        assertEquals(details.getStartTime(), result.getStartTime());
        assertEquals(details.getEndTime(), result.getEndTime());
        assertEquals(details.getPatientId(), result.getPatient().getId());
        assertEquals(practitioner.getId(), result.getPractitioner().getId());
        assertEquals(appointment.getId(), result.getAppointment().getId());
    }

    private static class TestObject {
        private int id;
        private String name;

        public TestObject() {
        }

        public TestObject(int id, String name) {
            this.id = id;
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            TestObject that = (TestObject) o;

            if (id != that.id) return false;
            return name != null ? name.equals(that.name) : that.name == null;
        }

        @Override
        public int hashCode() {
            int result = id;
            result = 31 * result + (name != null ? name.hashCode() : 0);
            return result;
        }
    }
}
