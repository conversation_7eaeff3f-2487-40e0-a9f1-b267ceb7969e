package com.nymbl.payment.service;

import com.nymbl.payment.dto.Branding;
import com.nymbl.payment.dto.ChargeRequest;
import com.nymbl.payment.dto.ConnectAccountSetting;
import com.nymbl.payment.utils.StripeUtil;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.StripeTransaction;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Account;
import com.stripe.model.File;
import com.stripe.model.LoginLink;
import com.stripe.model.checkout.Session;
import com.stripe.net.RequestOptions;
import com.stripe.param.PayoutCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class StripeService {

    private final StripeUtil stripeUtil;

    @Value("${stripe.secret.api-key}")
    private String secretKey;

    public StripeService(StripeUtil stripeUtil) {
        this.stripeUtil = stripeUtil;
    }

    public boolean updateConnectAccountSettings(ConnectAccountSetting connectAccountSetting) {
        Stripe.apiKey = secretKey;

        try {
            //Optional<String> iconId = uploadFile("", "business_icon");
            //Optional<String> logoId = uploadFile("", "business_logo");


            Account account = Account.retrieve(connectAccountSetting.getConnectAccountId());

            if (null != account) {
                Account.SettingsBranding brand = account.getSettings().getBranding();
                if (null != brand) {
                    brand.setPrimaryColor(connectAccountSetting.getBranding().getPrimaryColor());
                    brand.setSecondaryColor(connectAccountSetting.getBranding().getSecondaryColor());
                    brand.setIcon(StringUtils.isNotBlank(connectAccountSetting.getBranding().getIcon()) ? connectAccountSetting.getBranding().getIcon() : "");
                    brand.setLogo(StringUtils.isNotBlank(connectAccountSetting.getBranding().getLogo()) ? connectAccountSetting.getBranding().getLogo() : "");
                }
                account.getSettings().setBranding(brand);
                Map<String, Object> params = new HashMap<>();
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("SettingsBrand - branchId " + connectAccountSetting.getBranchId(), brand);
                params.put("metadata", metadata);
                account.update(params);
            }

        } catch (Exception e) {
            log.error("Stripe: Exception updating connect account settings!!!", e);
        }

        return false;
    }

    public String retrievePaymentIntent(String checkoutSessionId, String connectedAccountId)  {
        Stripe.apiKey = secretKey;
        try
        {
            RequestOptions requestOptions = RequestOptions.builder().build();

            if (StringUtils.isNotBlank(connectedAccountId)) {
                requestOptions = RequestOptions.builder()
                        .setStripeAccount(connectedAccountId)
                        .build();
            }


            Session session = Session.retrieve(checkoutSessionId, requestOptions);
            if (null != session) {
                return session.getPaymentIntent();
            }
        } catch (StripeException e) {
            log.error("Stripe: Exception retrieving payment intent!!!", e);
        }
        return "";
    }


    public Optional<File> retrieveStripeFile(String id) {
        Stripe.apiKey = secretKey;
        try
        {
            File file = File.retrieve(id);
            if (null != file) {
                return Optional.of(file);
            }
        } catch (StripeException e) {
            log.error("Stripe: Exception retrieving file from stripe!!!");
        }

        return Optional.empty();
    }

    private Optional<String> uploadFile(String path, String type) {
        Stripe.apiKey = secretKey;

        Map<String, Object> fileParams = new HashMap<>();
        fileParams.put("purpose", type);
        fileParams.put("file", new java.io.File(path));
        try
        {
            File file = File.create(fileParams);
            if (null != file) {
                return Optional.of(file.getId());
            }
        } catch (StripeException e) {
            log.error("Stripe: Exception uploading file to stripe!!!");
        }

        return Optional.empty();
    }

    public Optional<ConnectAccountSetting> retrieveConnectAccountSettings(Long branchId) {
        Stripe.apiKey = secretKey;

        try {
            Account account =
                    Account.retrieve(stripeUtil.getStripeAccount(branchId));
            Account.SettingsBranding branding = account.getSettings().getBranding();

            System.out.println(branding);
            Branding nymblBrand = new Branding(branding.getPrimaryColor(), branding.getIcon(), branding.getLogo(), branding.getSecondaryColor());
            return Optional.of(new ConnectAccountSetting(nymblBrand, account.getId()));
        } catch (StripeException e) {
            log.error("Stripe: Exception getting connect account settings!!!", e);
        }

        return Optional.empty();
    }


    /**
     *
     * This enables users to view dashboard of our services from within nymbl
     * @param branchId branch Id
     * @return LoginLink
     */
    public LoginLink createDashboardLoginLink(Long branchId) {
        // Use the userId to check access roles and branch Id to get connect account id
        try {
            return LoginLink.createOnAccount(
                    stripeUtil.getStripeAccount(branchId),
                    (Map<String, Object>) null,
                    null
            );
        } catch (StripeException e) {
            log.error("Stripe: Exception creating stripe dashboard link!!!");
        }

        return null;
    }

    /**
     *
     * Creates a checkout session
     * @param chargeRequest nymbl charge object
     * @return map
     */
    public Map<String, String> triggerCheckOut(ChargeRequest chargeRequest) {
        Stripe.apiKey = secretKey;
        SessionCreateParams.PaymentIntentData paymentIntentData = stripeUtil.calcNymblCharge(chargeRequest);
        BigDecimal multiply = new BigDecimal("0.03");
        StripeTransaction stripeTransaction = stripeUtil.saveStripePayment(chargeRequest, chargeRequest.getAmount().multiply(multiply));
        String stripeIdentifier = stripeUtil.makeStripeIdentifier(stripeTransaction.getId());
        chargeRequest.setStripeIdentifier(stripeIdentifier);

        String redirectSuccess = stripeUtil.getCallbackUrl(chargeRequest);
        Session session;

        Map<String, String> responseMap = new HashMap<>();
        try {
            SessionCreateParams.Builder paramsBuilder =
                    SessionCreateParams.builder()
                            .setMode(SessionCreateParams.Mode.PAYMENT)
                            .setSuccessUrl(redirectSuccess)
                            .setCancelUrl(redirectSuccess)
                            .setClientReferenceId(String.valueOf(chargeRequest.getStripeIdentifier()))
                            .addPaymentMethodType(SessionCreateParams.PaymentMethodType.CARD)
                            .setPaymentIntentData(paymentIntentData)
                            .putMetadata("tenant", TenantContext.getCurrentTenant())
                            .putMetadata("nymblPaymentMethod", chargeRequest.getNymblPaymentType())
                            .addLineItem(
                                    SessionCreateParams.LineItem.builder()
                                            .setQuantity(1L)
                                            .setPriceData(getPriceData(chargeRequest))
                                            .setDescription(stripeUtil.makeDescription(chargeRequest))
                                            .build());

            if (StringUtils.isNotBlank(chargeRequest.getPatientEmail()))
                paramsBuilder.setCustomerEmail(chargeRequest.getPatientEmail());

            SessionCreateParams params = paramsBuilder.build();
            RequestOptions requestOptions = RequestOptions.builder().setStripeAccount(stripeUtil.getStripeAccount(chargeRequest.getBranchId())).build();

            session = Session.create(params, requestOptions);
            responseMap.put("url", session.getUrl());
        }
        catch(Exception ex) {
            responseMap.put("exception", ex.getMessage());
            log.error("Stripe: Exception creating stripe checkout session: " + ex.getMessage());
            stripeUtil.deleteStripeTransaction(stripeTransaction);
        }

        return responseMap;
    }




    /**
     *
     * Method here to enable feature if needed for instant payout
     * @param chargeRequest nymbl charge object
     * @return PayoutCreateParams
     */
    public PayoutCreateParams createInstantPayout(ChargeRequest chargeRequest) {

        Long amount = chargeRequest.getAmount().setScale(0, RoundingMode.CEILING).longValue();

        return PayoutCreateParams.builder()
                .setAmount(amount)
                .setCurrency("usd")
                .setMethod(PayoutCreateParams.Method.INSTANT)
                .build();
    }

    /**
     *
     * Create charge for session object
     * @param request nymbl charge object
     * @return SessionCreateParams.LineItem.PriceData
     */
    private SessionCreateParams.LineItem.PriceData getPriceData(ChargeRequest request) {


        return SessionCreateParams.LineItem.PriceData.builder()
                .setCurrency(ChargeRequest.Currency.USD.name())
                .setUnitAmountDecimal(request.getAmount().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP))
                .setProductData(
                        SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                .setName(makePaymentName(request))
                                .build())
                .build();
    }

    /**
     * Line Item Name
     *
     * @param chargeRequest nymbl charge object
     * @return patient name
     */
    private String makePaymentName(ChargeRequest chargeRequest) {
        return chargeRequest.getPatientFullName();
    }

}
