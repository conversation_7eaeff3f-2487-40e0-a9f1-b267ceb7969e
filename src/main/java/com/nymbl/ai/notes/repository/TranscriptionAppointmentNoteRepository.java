package com.nymbl.ai.notes.repository;

import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface TranscriptionAppointmentNoteRepository extends JpaRepository<TranscriptionAppointmentNote, Long> {
    TranscriptionAppointmentNote findByTranscriptionDetailId(Long transcriptionDetailsId);

    @Transactional
    @Modifying
    @Query("UPDATE TranscriptionAppointmentNote t SET t.reviewedNotes = :reviewedNotes WHERE t.id = :id")
    void updateReviewedNotes(String reviewedNotes, Long id);
}
