package com.nymbl.ai.notes.controller;


import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.service.HealthScribeService;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.CompanyService;
import com.nymbl.tenant.TenantContext;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/healthscribe")
@Slf4j
public class HealthScribeController {

    private final TranscriptionUtil transcriptionUtil;
    @Value("${healthscribe.lambda.event}")
    private String lambdaEvent;

    private final HealthScribeService healthScribeService;
    private final CompanyService companyService;
    private final AwsUtil awsUtil;

    public HealthScribeController(HealthScribeService healthScribeService, CompanyService companyService, AwsUtil awsUtil, TranscriptionUtil transcriptionUtil) {
        this.healthScribeService = healthScribeService;
        this.companyService = companyService;
        this.awsUtil = awsUtil;
        this.transcriptionUtil = transcriptionUtil;
    }


    @PostMapping(value = "/upload-audio", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> uploadAudio(@RequestParam("file") MultipartFile file, Long practitionerId, Long appointmentId, Long patientId, Long prescriptionId, Long branchId, String action, double audioLength) {

        log.info("AWSConfig: Begin audio file Upload to HealthScribe");
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setPrescriptionId(prescriptionId);
        request.setBranchId(branchId);
        request.setAppointmentId(appointmentId);
        request.setPatientId(patientId);
        request.setPractitionerId(practitionerId);
        request.setAction(action);

        request.setFileExtension(action.equals("record") ? ".webm" : transcriptionUtil.getFileExtension(file));

        if (action.equals("record")) {
            request.setAudioLength(audioLength);
        }
        else
        {
            request.setAudioLength(transcriptionUtil.calculateAudioLength(file));
        }

        Long detailsId = healthScribeService.initiateRecording(request);
        String response = healthScribeService.uploadAudioFileToS3(file, detailsId);
        log.info("AWSConfig: End audio file Upload to HealthScribe");
        if ("success".equals(response))
            return ResponseEntity.ok(response);
        else
            return ResponseEntity.internalServerError().body(response);
    }

    /**
     * STOP: This is AI Notes maintenance endpoint only
     *
     * This method is used to set up S3 buckets and lambda events for AI Notes tenants
     *
     *
     * @return success message
     */
    @GetMapping(value = "/create-bucket", produces = MediaType.APPLICATION_JSON_VALUE)
    @Hidden
    public ResponseEntity<?> createBucket() {

        String tenant = TenantContext.getCurrentTenant();
        log.info("AWSConfig: Begin create s3 bucket for HealthScribe");
        if (companyService.findByKey(tenant) == null) {
            return ResponseEntity.internalServerError().body("Invalid tenant Provided");
        }

        AWSS3Service awss3Service = awsUtil.getUsEast1Client();
        String bucketName = healthScribeService.getOutputBucketName(tenant);

        if (awss3Service.doesBucketExist(bucketName)) {
            return ResponseEntity.ok("Bucket already exists!!!");
        }

        awss3Service.createBucketAndAddLambdaTrigger(bucketName, lambdaEvent);
        log.info("AWSConfig: End create s3 bucket for HealthScribe");
        return ResponseEntity.ok("success");
    }

}
