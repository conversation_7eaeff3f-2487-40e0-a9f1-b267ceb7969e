package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.ReviewedNoteRequest;
import com.nymbl.ai.notes.dto.TranscriptionAppointmentNotesDto;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.service.TranscriptionAppointmentNoteService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("api/transcribe/")
public class TranscriptionAppointmentNoteController {

    private final TranscriptionAppointmentNoteService transcriptionAppointmentNoteService;

    public TranscriptionAppointmentNoteController(TranscriptionAppointmentNoteService transcriptionAppointmentNoteService) {
        this.transcriptionAppointmentNoteService = transcriptionAppointmentNoteService;
    }

    @GetMapping(value = "search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "practitionerId", required = false) List<Long> practitionerIds,
                                    @RequestParam(name = "patientId", required = false) Long patientId,
                                    @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date startDate,
                                    @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date endDate,
                                    @RequestParam(name = "appointmentId", required = false) Long appointmentId,
                                    @RequestParam(name = "status", required = false) String status,
                                    @PageableDefault(page = 0, value = 1000) Pageable pageable,
                                    HttpServletRequest request) {
        Optional<List<TranscriptionDetailsDto>> results = transcriptionAppointmentNoteService.search(practitionerIds, patientId, appointmentId, startDate, endDate, status, pageable);


        if (results.isPresent())
            return ResponseEntity.ok(results.get());

        return ResponseEntity.noContent().build();
    }

    @GetMapping(value = "/{transcriptionAppointmentNotesId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> view(@PathVariable Long transcriptionAppointmentNotesId) {
        Optional<TranscriptionAppointmentNotesDto> results = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionAppointmentNotesId);

        if (results.isPresent())
            return ResponseEntity.ok(results.get());

        return ResponseEntity.internalServerError().build();
    }

    @GetMapping(value = "/details/{transcriptionDetailsId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> details(@PathVariable Long transcriptionDetailsId) {
        Optional<TranscriptionDetailsDto> results = transcriptionAppointmentNoteService.getDetailsById(transcriptionDetailsId);

        if (results.isPresent())
            return ResponseEntity.ok(results.get());

        return ResponseEntity.internalServerError().build();
    }

    @GetMapping(value = "/note/{transcriptionDetailsId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> notes(@PathVariable Long transcriptionDetailsId) {
        Optional<TranscriptionAppointmentNotesDto> results = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);

        if (results.isPresent())
        {
            TranscriptionAppointmentNotesDto notesDto = results.get();
            return ResponseEntity.ok(transcriptionAppointmentNoteService.getClinicalNotesString(notesDto.getClinicalDocumentation()));
        }
        return ResponseEntity.internalServerError().build();
    }

    @GetMapping(value = "/transcript/{transcriptionDetailsId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> transcript(@PathVariable Long transcriptionDetailsId) {
        Optional<TranscriptionAppointmentNotesDto> results = transcriptionAppointmentNoteService.viewGeneratedNotes(transcriptionDetailsId);

        if (results.isPresent())
        {
            TranscriptionAppointmentNotesDto notesDto = results.get();
            return ResponseEntity.ok(transcriptionAppointmentNoteService.getTranscriptsString(notesDto.getConversation()));
        }

        return ResponseEntity.internalServerError().build();
    }

    @PostMapping(value = "save-review", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> saveReviewedNotes(@RequestBody ReviewedNoteRequest request) {
        String response = transcriptionAppointmentNoteService.reviewNotes(request);
        if ("success".equals(response))
            return ResponseEntity.ok(response);
        else
            return ResponseEntity.internalServerError().body(response);
    }
}
