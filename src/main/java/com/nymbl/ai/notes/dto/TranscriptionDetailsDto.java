package com.nymbl.ai.notes.dto;


import com.nymbl.master.model.UserDto;
import com.nymbl.tenant.dashboard.dto.AppointmentDto;
import com.nymbl.tenant.dashboard.dto.PatientDto;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class TranscriptionDetailsDto {
    private Long id;
    private String jobName;
    private Long branchId;
    private AINOTE status;
    private UserDto createdBy;
    /**
     * When transcription starts.
     */
    private Timestamp startTime;
    private Timestamp endTime;
    private Timestamp updatedAt;
    private PatientDto patient;
    private UserDto practitioner;
    private AppointmentDto appointment;
    private Long prescriptionId;
    private String audioTime;
}
