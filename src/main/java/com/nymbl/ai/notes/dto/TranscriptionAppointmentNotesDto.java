package com.nymbl.ai.notes.dto;

import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.conversation.Conversation;
import lombok.Getter;
import lombok.Setter;

// TODO add createdAt and CreatedById
// TODO discuss with <PERSON> having me add the appt, the practitioner and patient on the model for note creation.
// should there be a subject passed?
// how do we re-fetch a note.
@Getter
@Setter
public class TranscriptionAppointmentNotesDto {
    private Conversation conversation;
    private ClinicalDocumentation clinicalDocumentation;
    /**
     * Concatenated clinical documentation sections.
     * TODO discuss getting HTML instead of Mark Down.
     */
    private String reviewNoteString;
    private String transcriptString;
    private Long transcriptionDetailsId;
    private Long transcriptionAppointmentNotesId;
    private Long noteId;
    private boolean isReviewed;
}
