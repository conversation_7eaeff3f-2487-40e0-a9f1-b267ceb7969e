package com.nymbl.ai.notes.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentationContainer;
import com.nymbl.ai.notes.data.clinicaldoc.Section;
import com.nymbl.ai.notes.data.conversation.Conversation;
import com.nymbl.ai.notes.data.conversation.ConversationContainer;
import com.nymbl.ai.notes.data.conversation.TranscriptSegment;
import com.nymbl.ai.notes.dto.*;
import com.nymbl.ai.notes.model.TranscriptionAppointmentNote;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionAppointmentNoteRepository;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.service.NoteService;
import com.nymbl.tenant.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TranscriptionAppointmentNoteService {

    private final TranscriptionDetailRepository transcriptionDetailRepository;
    private final TranscriptionAppointmentNoteRepository transcriptionAppointmentNoteRepository;
    private final TranscriptionUtil transcriptionUtil;
    private final NotificationService notificationService;
    private final NoteService noteService;
    private final AwsUtil awsUtil;

    public TranscriptionAppointmentNoteService(TranscriptionDetailRepository transcriptionDetailRepository, TranscriptionAppointmentNoteRepository transcriptionAppointmentNoteRepository, TranscriptionUtil transcriptionUtil, NotificationService notificationService, NoteService noteService, AwsUtil awsUtil) {
        this.transcriptionDetailRepository = transcriptionDetailRepository;
        this.transcriptionAppointmentNoteRepository = transcriptionAppointmentNoteRepository;
        this.transcriptionUtil = transcriptionUtil;
        this.notificationService = notificationService;
        this.noteService = noteService;
        this.awsUtil = awsUtil;
    }

    public Optional<List<TranscriptionDetailsDto>> search(List<Long> practitionerId, Long patientId, Long appointmentId, Date startDate, Date endDate, String status, Pageable pageable) {
        try
        {
            Specification<TranscriptionDetail> transcriptionDetailsSpecification = TranscriptionDetailSpecs.search(practitionerId, patientId, appointmentId, startDate, endDate, status);
            List<TranscriptionDetail> results = transcriptionDetailRepository.findAll(transcriptionDetailsSpecification, pageable).getContent();
            return Optional.of(results.stream()
                    .map(transcriptionUtil::convertToDto)
                    .sorted(new TranscriptionStatusComparator())
                    .collect(Collectors.toList()));
        }
        catch(Exception ex) {
            log.error("Transcription: Exception searching for transcription list ", ex);
        }
        return Optional.empty();
    }

    public Optional<TranscriptionDetailsDto> getDetailsById(Long detailsId) {
        try
        {
            Optional<TranscriptionDetail> details = transcriptionDetailRepository.findById(detailsId);
            if(details.isPresent()) {
                return Optional.of(transcriptionUtil.convertToDto(details.get()));
            }
        }
        catch(Exception ex) {
            log.error("Transcription: Exception finding transcription by id ", ex);
        }
        return Optional.empty();
    }

    public boolean writeCompletedNotesFromBucket(AiNoteWebhookDto aiNoteWebhookDto) {

        AWSS3Service awss3Service = awsUtil.getUsEast1Client();
        Optional<byte[]> summaryBytes = awss3Service.getFile(aiNoteWebhookDto.getBucketName(), aiNoteWebhookDto.getJobName() + "/summary.json");
        Optional<byte[]> transcriptBytes = awss3Service.getFile(aiNoteWebhookDto.getBucketName(), aiNoteWebhookDto.getJobName() + "/transcript.json");

        Long detailId = transcriptionUtil.removeLeadingZeroes(aiNoteWebhookDto.getJobName());
        log.info("Transcription: Transcription detail id {} ", detailId);
        if (null != transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(detailId)) {
            log.info("Transcription: Transcription detail id {} already processed", detailId);
            return false;
        }

        Optional<TranscriptionDetail> transcriptionDetailsOptional = transcriptionDetailRepository.findById(detailId);

        TranscriptionAppointmentNote transcriptionAppointmentNote = new TranscriptionAppointmentNote();
        String summaryJson = new String(summaryBytes.get(), StandardCharsets.UTF_8);
        String transcriptJson = new String(transcriptBytes.get(), StandardCharsets.UTF_8);

        TranscriptionDetail transcriptionDetail = transcriptionDetailsOptional.get();
        transcriptionDetail.setStatus(AINOTE.READY);

        Instant instant = Instant.parse(aiNoteWebhookDto.getEventTime());
        Timestamp endTime = Timestamp.from(instant);
        transcriptionDetail.setEndTime(endTime);
        transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionDetail.setUpdatedById(transcriptionDetail.getCreatedById());


        transcriptionAppointmentNote.setTranscriptionDetailId(transcriptionDetail.getId());
        transcriptionAppointmentNote.setGenerateClinicalNotes(summaryJson);
        transcriptionAppointmentNote.setGeneratedConversation(transcriptJson);
        transcriptionAppointmentNote.setReviewed(false);
        transcriptionAppointmentNote.setCreatedAt(endTime);
        transcriptionAppointmentNote.setCreatedById(transcriptionDetail.getCreatedById());
        transcriptionAppointmentNote.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionAppointmentNote.setUpdatedById(transcriptionDetail.getCreatedById());


        transcriptionAppointmentNoteRepository.save(transcriptionAppointmentNote);
        transcriptionDetailRepository.save(transcriptionDetail);
        notificationService.createAINoteNotification(transcriptionDetail);

        return true;
    }

    public Optional<TranscriptionAppointmentNotesDto> viewGeneratedNotes(Long transcriptionDetailsId) {

        TranscriptionAppointmentNote transcriptionAppointmentNote = transcriptionAppointmentNoteRepository.findByTranscriptionDetailId(transcriptionDetailsId);
        TranscriptionAppointmentNotesDto transcriptionAppointmentNotesDto = new TranscriptionAppointmentNotesDto();
        ConversationContainer conversation;
        ClinicalDocumentationContainer clinicalDocumentation;

        try
        {
            conversation = transcriptionUtil.fromJson(transcriptionAppointmentNote.getGeneratedConversation(), ConversationContainer.class);
            clinicalDocumentation = transcriptionUtil.fromJson(transcriptionAppointmentNote.getGenerateClinicalNotes(), ClinicalDocumentationContainer.class);


            transcriptionAppointmentNotesDto.setTranscriptionDetailsId(transcriptionDetailsId);
            transcriptionAppointmentNotesDto.setTranscriptionAppointmentNotesId(transcriptionAppointmentNote.getId());
            transcriptionAppointmentNotesDto.setReviewed(transcriptionAppointmentNote.isReviewed());
            transcriptionAppointmentNotesDto.setConversation(conversation.getConversation());
            transcriptionAppointmentNotesDto.setClinicalDocumentation(clinicalDocumentation.getClinicalDocumentation());
            transcriptionAppointmentNotesDto.setReviewNoteString(getClinicalNotesString(clinicalDocumentation.getClinicalDocumentation()));
            transcriptionAppointmentNotesDto.setTranscriptString(getTranscriptsString(conversation.getConversation()));

        } catch (JsonProcessingException e) {
            log.error("Transcription: Exception while retrieving notes for review {}", transcriptionDetailsId, e);
            return Optional.empty();
        }
        return Optional.of(transcriptionAppointmentNotesDto);
    }

    /**
     *
     *
     * @param request
     * @return String response success or failure
     */
    public String reviewNotes(ReviewedNoteRequest request) {

        String message = "success";
        Optional<TranscriptionAppointmentNote> transcriptionAppointmentNotesOpt = transcriptionAppointmentNoteRepository.findById(request.getTranscriptionAppointmentNoteId());
        TranscriptionAppointmentNote transcriptionAppointmentNote = transcriptionAppointmentNotesOpt.get();



        Optional<TranscriptionDetail> transcriptionDetailsOptional = transcriptionDetailRepository.findById(transcriptionAppointmentNote.getTranscriptionDetailId());
        TranscriptionDetail transcriptionDetail = transcriptionDetailsOptional.get();
        transcriptionDetail.setStatus(AINOTE.PUBLISHED);
        transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionDetail.setUpdatedById(transcriptionDetail.getCreatedById());
        transcriptionDetailRepository.save(transcriptionDetail);

        Note note = new Note();
        note.setNote(request.getReviewedNoteString());
        note.setNoteType("clinical");
        note.setPrescriptionId(request.getPrescriptionId());
        note.setAppointmentId(request.getAppointmentId());
        note.setPatientId(request.getPatientId());
        note.setCreatedAt(Timestamp.from(Instant.now()));
        note.setUpdatedAt(Timestamp.from(Instant.now()));
        note.setTreatingPractitionerId(request.getTreatingPractitionerId());
        note.setSubject(request.getSubject());
        note.setPublished(true);
        note.setTranscriptionDetailId(transcriptionAppointmentNote.getTranscriptionDetailId());
        Map<String, Object> noteMap = noteService.saveNote(note, request.getAction());


        Note savedNote = (Note) noteMap.get(OptimisticLockingUtil.SAVED);
        transcriptionAppointmentNote.setNoteId(savedNote.getId());
        transcriptionAppointmentNote.setReviewed(true);
        transcriptionAppointmentNoteRepository.save(transcriptionAppointmentNote);

        return message;
    }


    /**
     * TODO: How exactly this response is displayed
     *
     * @param clinicalDocumentation clinical documents
     * @return String of output document
     */
    public String getClinicalNotesString(ClinicalDocumentation clinicalDocumentation) {
        StringBuilder stringBuilder = new StringBuilder();
        List<Section> sections = clinicalDocumentation.getSections();

        for (Section section : sections) {
            stringBuilder.append(section.printSectionDetails());
            stringBuilder.append("<br />");
        }
        return HtmlUtils.htmlEscape(stringBuilder.toString());
    }

    /**
     * TODO: How exactly this response is displayed
     *
     * @param conversation
     * @return
     */
    public String getTranscriptsString(Conversation conversation) {
        StringBuilder stringBuilder = new StringBuilder();
        List<TranscriptSegment> segments = conversation.getTranscriptSegments();

        for (TranscriptSegment segment : segments) {
            stringBuilder.append(segment.printTranscriptSegment());
        }
        return stringBuilder.toString();
    }
 }
