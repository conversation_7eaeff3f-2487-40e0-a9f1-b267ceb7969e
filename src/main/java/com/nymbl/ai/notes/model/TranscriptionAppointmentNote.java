package com.nymbl.ai.notes.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nymbl.config.utils.JsonDateTimeDeserializer;
import com.nymbl.config.utils.JsonDateTimeSerializer;
import com.nymbl.master.model.User;
import com.nymbl.tenant.model.Note;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
@Audited
@Table(name = "transcription_appointment_note")
public class TranscriptionAppointmentNote {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "transcription_detail_id", nullable = false)
    private Long transcriptionDetailId;

    @Column(name = "note_id")
    private Long noteId;

    @Column(name = "subject", nullable = false)
    private String subject;

    @Column(name = "generated_conversation", columnDefinition = "text")
    private String generatedConversation;

    @Column(name = "generated_clinical_notes", columnDefinition = "text")
    private String generateClinicalNotes;

    @Column(name = "reviewed_notes", columnDefinition = "text")
    private String reviewedNotes;

    @Column(name = "created_by_id", nullable = false)
    private Long createdById;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    @Column(name = "created_at", columnDefinition = "TIMESTAMP")
    private Timestamp createdAt;

    @Column(name = "updated_by_id", nullable = false)
    private Long updatedById;

    @JsonSerialize(using = JsonDateTimeSerializer.class)
    @JsonDeserialize(using = JsonDateTimeDeserializer.class)
    @Column(name = "updated_at", columnDefinition = "TIMESTAMP")
    private Timestamp updatedAt;

    @Column(name = "is_reviewed")
    private boolean isReviewed = false;

    @ManyToOne
    @JoinColumn(name = "transcription_detail_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptionaudiotranscriptiondetailid"))
    private TranscriptionDetail transcriptionDetail;

    @ManyToOne
    @JoinColumn(name = "note_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptionappointmentnotenoteid"))
    private Note note;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "created_by_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailcreatedbyid"))
    private User createdBy;

    @NotAudited
    @Transient
    @ManyToOne
//    @JoinColumn(name = "updated_by_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "FK_transcriptiondetailupdatedbyid"))
    private User updatedBy;
}
