package com.nymbl.tenant.dashboard.dto;

import org.springframework.beans.factory.annotation.Value;

import java.time.OffsetDateTime;

public interface AppointmentDto extends PatientInfoDto {
    Long getId();

    String getAppointmentType();

    @Value("#{(T(com.nymbl.config.utils.StringUtil).isBlank(target.startDateTime) ? null : T(java.time.OffsetDateTime).parse(target.startDateTime, T(java.time.format.DateTimeFormatter).ISO_OFFSET_DATE_TIME))}")
    OffsetDateTime getStartDateTime();

    @Value("#{(T(com.nymbl.config.utils.StringUtil).isBlank(target.endDateTime) ? null : T(java.time.OffsetDateTime).parse(target.endDateTime, T(java.time.format.DateTimeFormatter).ISO_OFFSET_DATE_TIME))}")
    OffsetDateTime getEndDateTime();
}
