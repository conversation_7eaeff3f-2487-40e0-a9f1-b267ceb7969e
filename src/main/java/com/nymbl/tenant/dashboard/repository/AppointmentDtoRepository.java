package com.nymbl.tenant.dashboard.repository;

import com.nymbl.tenant.dashboard.dto.AppointmentDto;
import com.nymbl.tenant.dashboard.dto.MissingAppointmentNotesDto;
import com.nymbl.tenant.model.Appointment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;

@Repository
public interface AppointmentDtoRepository extends JpaRepository<Appointment, Long>, JpaSpecificationExecutor<Appointment> {

    @Query(value = APPOINTMENT_DTO_QUERY, nativeQuery = true)
    AppointmentDto getAppointmentDtoById(Long id);

    @Query(value = "SELECT COUNT(id) as appointment_count FROM appointment WHERE branch_id = :branchId AND start_datetime >= :startDateTime", nativeQuery = true)
    Long getAppointmentCountByBranchAndStartDateTime(Long branchId, OffsetDateTime startDateTime);

    @Query(value = MISSING_NOTES_QUERY, nativeQuery = true)
    List<MissingAppointmentNotesDto> getMissingNotes(OffsetDateTime startDateTime, OffsetDateTime endDateTime, OffsetDateTime now, Long userId);

    @Query(value = MISSING_NOTES_DASHBOARD_QUERY, nativeQuery = true)
    List<MissingAppointmentNotesDto> getMissingNotesForDashboard(Long userId, OffsetDateTime now);

    @Query(value = UPCOMING_APPOINTMENTS_BY_USER_QUERY, nativeQuery = true)
    List<AppointmentDto> getUpcomingAppointmentsByUser(Long userId, OffsetDateTime today);

    String APPOINTMENT_DTO_FIELDS = " a.id, DATE_FORMAT(a.start_datetime,'%Y-%m-%dT%TZ') AS startDateTime, " +
            "DATE_FORMAT(a.end_datetime,'%Y-%m-%dT%TZ') AS endDateTime,  " +
            "at.name AS appointmentType, p.id AS patientId, p.first_name as firstName, p.last_name as lastName " + "FROM appointment a   " +
            "JOIN appointment_type at ON a.appointment_type_id = at.id    " +
            "LEFT JOIN patient p ON a.patient_id = p.id   ";
    String APPOINTMENT_DTO_QUERY = "SELECT" + APPOINTMENT_DTO_FIELDS + "WHERE a.id = :id";

    String UPCOMING_APPOINTMENTS_BY_USER_QUERY = "SELECT DISTINCT" + APPOINTMENT_DTO_FIELDS +
            "WHERE  (a.user_id = :userId OR a.user_two_id = :userId OR a.user_three_id = :userId)  " +
            "AND a.status NOT IN ('cancelled')   " +
            "AND a.end_datetime >= :today " +
            "ORDER BY endDateTime ASC limit 30";

    String MISSING_NOTES_QUERY = "SELECT DISTINCT a.id, DATE_FORMAT(a.start_datetime,'%Y-%m-%dT%TZ') AS startDateTime, " +
            " DATE_FORMAT(a.end_datetime,'%Y-%m-%dT%TZ') AS endDateTime, a.user_id as attendingUserId, a.user_two_id as userTwoId, a.user_three_id as userThreeId, a.user_four_id as supervisingUserId, a.status as status," +
            " a.prescription_id as prescriptionId, a.prescription_two_id as prescriptionTwoId, at.name AS appointmentType, p.id AS patientId, p.first_name as firstName, p.last_name as lastName, b.name AS branch   " +
            "FROM appointment a   " +
            "   JOIN appointment_type at ON a.appointment_type_id = at.id  " +
            "   JOIN patient p ON a.patient_id = p.id   " +
            "   JOIN branch b ON a.branch_id = b.id   " +
            "WHERE a.id NOT IN (   " +
            "   SELECT DISTINCT a1.id    " +
            "    FROM appointment a1   " +
            "   JOIN (SELECT DISTINCT appointment_id, published FROM note n1 JOIN appointment a2 ON n1.appointment_id = a2.id WHERE published = true AND (note_type = 'clinical' OR a2.start_datetime < \"2023-06-23 02:00:00\")) as n ON a1.id = n.appointment_id)   " +
            "AND a.status NOT IN ('unconfirmed', 'cancelled', 'no_answer', 'miscellaneous', 'no_show','rejected','rescheduled','left_message')   " +
            "AND a.start_datetime BETWEEN :startDateTime AND :endDateTime " +
            "AND a.start_datetime <= :now " +
            "AND a.patient_id IS NOT NULL   " +
            "AND (:userId is null OR a.user_id = :userId OR  a.user_two_id = :userId OR a.user_three_id = :userId) " +
            "ORDER BY startDateTime";

    String MISSING_NOTES_DASHBOARD_QUERY = "SELECT DISTINCT a.id, DATE_FORMAT(a.start_datetime,'%Y-%m-%dT%TZ') AS startDateTime, " +
            " DATE_FORMAT(a.end_datetime,'%Y-%m-%dT%TZ') AS endDateTime, a.user_id as attendingUserId, a.user_two_id as userTwoId, a.user_three_id as userThreeId, a.user_four_id as supervisingUserId, a.status as status," +
            " a.prescription_id as prescriptionId, a.prescription_two_id as prescriptionTwoId, at.name AS appointmentType, p.id AS patientId, p.first_name as firstName, p.last_name as lastName, b.name AS branch   " +
            "FROM appointment a   " +
            "   JOIN appointment_type at ON a.appointment_type_id = at.id  " +
            "   JOIN patient p ON a.patient_id = p.id   " +
            "   JOIN branch b ON a.branch_id = b.id   " +
            "WHERE a.start_datetime <= :now " +
            "AND a.id NOT IN (   " +
            "   SELECT DISTINCT a1.id    " +
            "    FROM appointment a1   " +
            "   JOIN (SELECT DISTINCT appointment_id, published FROM note n1 JOIN appointment a2 ON n1.appointment_id = a2.id WHERE published = true AND (note_type = 'clinical' OR a2.start_datetime < \"2023-06-23 02:00:00\")) as n ON a1.id = n.appointment_id)   " +
            "AND a.status NOT IN ('unconfirmed', 'cancelled', 'no_answer', 'miscellaneous', 'no_show','rejected','rescheduled','left_message')   " +
            "AND (a.user_id = :userId OR a.user_two_id = :userId OR a.user_three_id = :userId) " +
            "AND a.patient_id IS NOT NULL   " +
            "ORDER BY startDateTime";
}
