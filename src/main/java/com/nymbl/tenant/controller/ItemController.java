package com.nymbl.tenant.controller;

import com.google.common.base.Strings;
import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.dto.HCPCSItemDTO;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Item;
import com.nymbl.tenant.model.Prescription_L_Code;
import com.nymbl.tenant.service.ItemService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

@RestController
@RequestMapping("/api/item")
public class ItemController extends AbstractController<Item, Long> {

    private final ItemService itemService;
    private final FileUtil fileUtil;
    private final UserService userService;

    @Autowired
    public ItemController(ItemService itemService,
                          FileUtil fileUtil,
                          UserService userService) {
        super(itemService);
        this.itemService = itemService;
        this.fileUtil = fileUtil;
        this.userService = userService;
    }

    @GetMapping(value = "/audit/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> audit(@PathVariable Long id) {
        try {
            List<Item> result = itemService.getAudit(id);
            return ResponseEntity.ok().body(result);
        } catch (Exception ex) {
            return ResponseEntity.badRequest().body(ex.getMessage());
        }
    }

    //    @Auditable(entry = "Search Item")
    @GetMapping(value = "/get-items", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getItems(@RequestParam(name = "columnName", required = false) String columnName,
                                      @RequestParam(name = "keywords", required = false) String keywords,
                                      @RequestParam(name = "lCodeCategoryId", required = false) Long lCodeCategoryId,
                                      @RequestParam(name = "oneTimePurchase", defaultValue = "false") Boolean oneTimePurchase,
                                      @RequestParam(name = "page") int page,
                                      @RequestParam(name = "showInactive", defaultValue = "false") Boolean showInactive,
                                      @RequestParam(name = "size") int size,
                                      @RequestParam(name = "sortDirection", required = false) String sortDirection,
                                      @RequestParam(name = "vendorId", required = false) Long vendorId) {
        if (Strings.isNullOrEmpty(columnName)) columnName = "name";
        if (Strings.isNullOrEmpty(sortDirection)) sortDirection = "ASC";
        Pageable pageable = sortDirection.equals("ASC")
                ? PageRequest.of(page, size, Sort.by(columnName).ascending())
                : PageRequest.of(page, size, Sort.by(columnName).descending());
        try {
            Page<Item> results = itemService.getItems(keywords, lCodeCategoryId, oneTimePurchase, pageable, showInactive, vendorId);
            return ResponseEntity.ok(results);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "keywords") String keywords,
                                    @RequestParam(name = "lCodeCategoryId", required = false) Long lCodeCategoryId,
                                    @RequestParam(name = "showInactive", required = false) Boolean showInactive,
                                    @RequestParam(name = "vendorId", required = false) Long vendorId) {
        List<Item> results = itemService.search(keywords, lCodeCategoryId, null, showInactive, vendorId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/parent/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByParentId(@PathVariable Long id) {
        Collection<Item> results = itemService.findByParentId(id);
        return ResponseEntity.ok(results);
    }

    @DeleteMapping(value = "/delete/{itemId}")
    public ResponseEntity<?> deleteItem(@PathVariable Long itemId) {
        String response = itemService.deleteItem(itemId);
        if ("OK".equals(response))
            return ResponseEntity.ok("{\"success\":true,\"message\":null}");
        else
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"success\":false,\"message\":\"" + response + "\"}");
    }

    @GetMapping(value = "/part_number/{partNumber}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPartNumber(@PathVariable String partNumber) {
        List<Item> result = itemService.findByPartNumber(partNumber);
        return ResponseEntity.ok(result);
    }

    //this is a POST because the PN can have special chars
    @PostMapping(value = "/part_number-vendor/vendor/{vendorId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPartNumberAndVendorId(@RequestBody String partNumber, @PathVariable Long vendorId) {
        Item result = itemService.findByPartNumberAndVendorId(partNumber, vendorId);
        return ResponseEntity.ok(result);
    }

    @GetMapping(value = "/find-all-by-name-and-sku", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findAllByNameAndSku(@RequestParam(name = "name", required = true) String name,
                                                 @RequestParam(name = "sku", required = false) String sku) {
        List<Item> results = itemService.findAllByNameAndSku(name, sku);
        return ResponseEntity.ok(results);
    }

    @PostMapping(value = "/read-price-catalog/{vendorId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> readPriceCatalog(@PathVariable Long vendorId,
                                              MultipartFile file) throws Exception {
        List<ItemService.Price> results = itemService.readPriceCatalog(fileUtil.convertToFile(file), vendorId);
        return ResponseEntity.ok(results);
    }

    @PostMapping(value = "/import-price-catalog/{vendorId}")
    public ResponseEntity<?> importPriceCatalog(@RequestBody List<ItemService.Price> prices,
                                                @PathVariable Long vendorId) {
        itemService.importPriceCatalog(prices, vendorId);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<?> save(@RequestBody Item item, HttpServletRequest request) {
        try {
            Map<String, Object> respMap = itemService.saveForVersion(item);
            Object savedObject = respMap.get(SAVED);
            if (savedObject != null) {
                return ResponseEntity.ok(savedObject);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(respMap);
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(StringUtil.getExceptionAsString(e));
        }
    }

    @PostMapping(value = "/hcpcs", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByHCPCS(@RequestBody List<Prescription_L_Code> prescriptionLCodes) {
        List<HCPCSItemDTO> results = itemService.findByHCPCS(prescriptionLCodes);
        return ResponseEntity.ok(results);
    }
}
