package com.nymbl.tenant.service;

import com.google.common.base.Strings;
import com.nymbl.config.dto.HCPCSItemDTO;
import com.nymbl.config.poquote.*;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.service.TableObjectContainer;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.ItemRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

@Slf4j
@Service
public class ItemService extends AbstractTableService<Item, Long> {

    private final InventoryItemService inventoryItemService;
    private final ItemByManufacturerService itemByManufacturerService;
    private final ItemRepository itemRepository;
    private final L_CodeCategoryService lCodeCategoryService;
    private final L_CodeService lCodeService;
    private Prescription_L_CodeService prescriptionLCodeService;
    private final PurchaseOrder_ItemService purchaseOrderItemService;
    private final PurchaseOrderService purchaseOrderService;
    private final ShoppingCartService shoppingCartService;
    private final UserService userService;
    private final VendorService vendorService;

    @Data
    public static class Price {
        private String partNumber;
        private BigDecimal oldPrice;
        private BigDecimal newPrice;
    }

    @Autowired
    public ItemService(InventoryItemService inventoryItemService,
                       ItemByManufacturerService itemByManufacturerService,
                       ItemRepository itemRepository,
                       L_CodeCategoryService lCodeCategoryService,
                       L_CodeService lCodeService,
                       Prescription_L_CodeService prescriptionLCodeService,
                       PurchaseOrder_ItemService purchaseOrderItemService,
                       PurchaseOrderService purchaseOrderService,
                       ShoppingCartService shoppingCartService,
                       UserService userService,
                       VendorService vendorService) {
        super(itemRepository);
        this.inventoryItemService = inventoryItemService;
        this.itemByManufacturerService = itemByManufacturerService;
        this.itemRepository = itemRepository;
        this.lCodeCategoryService = lCodeCategoryService;
        this.lCodeService = lCodeService;
        this.prescriptionLCodeService = prescriptionLCodeService;
        this.purchaseOrderItemService = purchaseOrderItemService;
        this.purchaseOrderService = purchaseOrderService;
        this.shoppingCartService = shoppingCartService;
        this.userService = userService;
        this.vendorService = vendorService;
    }

    public Map<Long, Item> createItemsFromQuote(VendorQuote quote) {
        Long vendorId = quote.getVendorId();
        VendorQuoteLineItem[] lines = quote.getLines();
        // line items don't have a reliable identifier, so I use their ordinal position
        Map<Long, Item> itemsMap = new HashMap<>();
        for (int i = 0; i < lines.length; i++) {
            VendorQuoteLineItem line = lines[i];
            if (!Strings.isNullOrEmpty(line.getPartNumber())) {
                BigDecimal cost = line.getDiscountedPrice();
                BigDecimal msrp = line.getMsrp();

                if ((quote.getIncludeZeroDollarItems() == null || quote.getIncludeZeroDollarItems() == false) && cost.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                Item item = findByPartNumberPriceAndVendorId(line.getPartNumber(), cost, vendorId);
                boolean needToSaveItem = false;
                long itemId;
                if (item == null) {
                    itemId = -(i + 1);
                    needToSaveItem = true;
                    String description = line.getDescription();
                    if (Strings.isNullOrEmpty(description)) {
                        description = line.getPartNumber();
                    }
                    // N.B.: here vendorId is used as manufacturerId
                    Long manufacturerId = vendorId;
                    ItemByManufacturer ibm = itemByManufacturerService.findOrCreate(
                            description, manufacturerId, line.getPartNumber(), null, line.getMsrp());
                    item = new Item();
                    item.setActive(true);
                    item.setCreatedById(null);
                    item.setItemByManufacturer(ibm);
                    item.setItemByManufacturerId(ibm.getId());
                    item.setPrice(cost);
                    item.setVendorId(vendorId);
                } else {
                    itemId = item.getId();
                    // TODO: this needs to be verified
                    if (msrp != null && (item.getPatientSalePrice() == null || msrp.compareTo(item.getPatientSalePrice()) != 0)) {
                        needToSaveItem = true;
                        item.setPatientSalePrice(msrp);
                    }
                    // this shouldn't happen since we are searching by price
                    if (cost != null && (item.getPrice() == null || cost.compareTo(item.getPrice()) != 0)) {
                        needToSaveItem = true;
                        item.setPrice(cost);
                    }
                }
                if (needToSaveItem) {
                    item = save(item);
                }
                // these will be used in createPurchaseOrderFromQuote()
                itemsMap.put(itemId, item);
            }
        }
        return itemsMap;
    }

    public String deleteItem(Long itemId) {
        String error = "OK";
        List<PurchaseOrder_Item> purchaseOrderItems = purchaseOrderItemService.findByItemId(itemId);
        if (purchaseOrderItems != null && !purchaseOrderItems.isEmpty()) {
            error = "The item could not be deleted because it is attached to an existing purchase order";
        } else {
            try {
                delete(itemId);
            } catch (Exception ex) {
                error = ex.getMessage();
            }
        }
        return error;
    }

    @Deprecated
    public List<Item> findAllByNameAndSku(String name, String sku) {
        List<Item> results = itemRepository.findAllByNameAndSku(name, sku);
        loadForeignKeysAllItems(results, false);
        return results;
    }

    public List<HCPCSItemDTO> findByHCPCS(List<Prescription_L_Code> prescriptionLCodes) {
        List<HCPCSItemDTO> results = new ArrayList<>();
        Long prescriptionId = prescriptionLCodes.get(0).getPrescriptionId();
        List<ShoppingCart> cartItems = shoppingCartService.findByPrescriptionId(prescriptionId);
        for (Prescription_L_Code plc : prescriptionLCodes) {
            HCPCSItemDTO dto = new HCPCSItemDTO();
            dto.setPrescriptionLCode(plc);
            List<Item> items = itemRepository.findByLCodeId(plc.getLCodeId());
            if (items != null && !items.isEmpty()) {
                dto.getItems().addAll(items);
            }
            for (ShoppingCart sc : cartItems) {
            	if (plc.getId().equals(sc.getPrescriptionLCodeId())) {
                    dto.getItemsNoHCPCS().add(sc); // itemsNoHCPCS are shopping items
            	}
            }
            results.add(dto);
        }
        return results;
    }

    public List<Item> findByPrescriptionLCodeId(Long prescriptionLCodeId) {
        List<Item> results = itemRepository.findByPrescriptionLCodeId(prescriptionLCodeId);
        loadForeignKeysList(results);
        return results;
    }

    @Deprecated
    public List<Item> findByParentId(Long parentId) {
        List<Long> lCodeCategoryIds = getChildrenIds(parentId);
        List<Item> results = itemRepository.findByLCodeCategoryIdIn(lCodeCategoryIds);
        loadForeignKeysAllItems(results, false);
        return results;
    }

    @Deprecated
    public List<Item> findByPartNumber(String partNumber) {
        List<Item> results = itemRepository.findByPartNumber(partNumber);
        loadForeignKeysAllItems(results, false);
        return results;
    }

    /**
     * Used by Maintenance -> Purchasing -> Items (New) and Purchasing -> All Purchase Orders -> PurchaseOrderItem.init()
     * @param partNumber
     * @param vendorId
     * @return
     */
    public Item findByPartNumberAndVendorId(String partNumber, Long vendorId) {
        Item item = itemRepository.findByPartNumberAndVendorId(partNumber, vendorId);
        loadForeignKeys(item);
        return item;
    }

    public Item findByPartNumberPriceAndVendorId(String partNumber, BigDecimal price, Long vendorId) {
        Item item = itemRepository.findByPartNumberPriceAndVendorId(partNumber, price, vendorId);
        loadForeignKeys(item);
        return item;
    }

    public Item getOne(Long itemId) {
        if (itemId != null && itemId > 0) {
            return itemRepository.getOne(itemId);
        } else {
            return null;
        }
    }
    public List<Item> getAudit(Long itemId) {
        return itemRepository.getAudit(itemId);
    }

    public List<Long> getChildrenIds(Long id) {
        if (id == null) {
            return new ArrayList<>();
        }
        List<Long> result = new ArrayList<>();
        ArrayList<L_CodeCategory> children = new ArrayList<>();
        List<L_CodeCategory> categories = lCodeCategoryService.getChildCategories(id, children);
        for (L_CodeCategory lCodeCategory : categories)
            result.add(lCodeCategory.getId());
        result.sort(Long::compareTo);
        return result;
    }

    /**
     * Used by Maintenance -> Purchasing -> Items (load and Search)
     * @param keywords
     * @param lCodeCategoryId
     * @param showInactive
     * @param pageable
     * @return
     */
    public Page<Item> getItems(String keywords, Long lCodeCategoryId, Boolean oneTimePurchase, Pageable pageable, Boolean showInactive, Long vendorId) {
        if (pageable == null) {
            pageable = PageRequest.of(0, 5000, Sort.by("name").ascending());
        }
        // Parameters
        List<Long> childCategoryIds = getChildrenIds(lCodeCategoryId);
        if (childCategoryIds == null || childCategoryIds.isEmpty()) childCategoryIds = null;
        String sku = keywords + "%";
        keywords = StringUtil.formatKeywordsForFullStringSearch(keywords);
        if (showInactive == null) showInactive = false;
        // Pageable native query
        Page<Item> result = itemRepository.getItems(keywords, childCategoryIds, oneTimePurchase, pageable, showInactive, sku, vendorId);
        if (result != null) {
            loadForeignKeysAllItems(result.getContent(), true);
        }
        return result;
    }

    /**
     * Used by Maintenance -> Purchasing -> Items (Import Price Catalog)
     * @param prices
     * @param vendorId
     */
    public void importPriceCatalog(List<ItemService.Price> prices, Long vendorId) {
        for (ItemService.Price p : prices) {
            Item item = findByPartNumberAndVendorId(p.getPartNumber(), vendorId);
            if (item != null) {
                item.setPrice(p.getNewPrice());
                save(item);
            }
        }
    }

    public List<ItemService.Price> readPriceCatalog(File file, Long vendorId) throws Exception {
        List<ItemService.Price> list = new ArrayList<>();
        FileInputStream fis = new FileInputStream(file);
        XSSFWorkbook myWorkBook = new XSSFWorkbook(fis);
        XSSFSheet mySheet = myWorkBook.getSheetAt(0);
        for (Row row : mySheet) {
            String partNumber;
            double p;
            Cell cell = row.getCell(0);
            if (cell.getCellType().equals(CellType.NUMERIC)) {
                partNumber = NumberToTextConverter.toText(cell.getNumericCellValue());
            } else {
                partNumber = cell.getStringCellValue();
            }
            try {
                p = row.getCell(1).getNumericCellValue();
            } catch (Exception e) {
                continue;
            }
            BigDecimal newPrice = new BigDecimal(p).setScale(2, RoundingMode.HALF_UP);
            Item item = itemRepository.findByPartNumberAndVendorId(partNumber, vendorId);
            if (item != null) {
                ItemService.Price price = new ItemService.Price();
                price.setPartNumber(partNumber);
                price.setOldPrice(item.getPrice());
                price.setNewPrice(newPrice);
                list.add(price);
            }
        }
        return list;
    }

    /**
     * This method is invoked from drop-down Item search fields in various parts of the application.
     * As such, it expects the search string (keywords) to be not empty.
     * @param keywords
     * @param lCodeCategoryId
     * @param vendorId
     * @param showInactive
     * @return
     */
    public List<Item> search(String keywords, Long lCodeCategoryId, Boolean oneTimePurchase, Boolean showInactive, Long vendorId) {
        // Parameters
        if (Strings.isNullOrEmpty(keywords)) {
            return new ArrayList<>();
        }
        List<Long> childCategoryIds = null;
        if (lCodeCategoryId != null && lCodeCategoryId > 0) {
            childCategoryIds = getChildrenIds(lCodeCategoryId);
            if (childCategoryIds == null || childCategoryIds.isEmpty()) childCategoryIds = null;
        }
        String sku = keywords + "%";
        keywords = StringUtil.formatKeywordsForFullStringSearch(keywords);
        // native query
        List<Item> result = itemRepository.search(keywords, childCategoryIds, oneTimePurchase, showInactive, sku, vendorId);
        if (result != null) {
            loadForeignKeysAllItems(result, true);
        }
        return result;
    }

    /**
     * to improve performance, I am using explicit iterations instead of List.stream()
     * @param items
     */
    private void loadForeignKeysAllItems(List<Item> items, boolean flattenCategory) {
        if (items != null && !items.isEmpty()) {
            // Create a list of userIds for a single query
            List<Long> userIds = new ArrayList<>();
            for (Item item : items) {
                Long userId = item.getCreatedById();
                if (userId != null && userId > 0 && !userIds.contains(userId)) {
                    userIds.add(userId);
                }
            }
            // Run the query
            List<User> users = userService.findAllUsersByIdList(userIds);
            // Assign to each item its corresponding user (createdBy)
            for (Item item : items) {
                // Needed for getItems()
                if (flattenCategory && item.getLCodeCategory() != null && item.getLCodeCategory().getParent() != null) {
                    item.getLCodeCategory().setParent(null);
                }
                Long userId = item.getCreatedById();
                if (userId != null && userId > 0) {
                    for (User user : users) {
                        if (userId.equals(user.getId())) {
                            item.setCreatedBy(user);
                            break;
                        }
                    }
                }
                /*
                ItemByManufacturer ibm = item.getItemByManufacturer();
                if (ibm != null) {
                    String description = ibm.getDescription();
                    if (!Strings.isNullOrEmpty(description) && description.contains("\"")) {
                        item.setDescription(description.replaceAll("\\\"", "&quot;"));
                    }
                }
                */
            }
        }
    }

    @Override
    public void loadForeignKeys(Item item) {
        if (item != null) {
            item.setCreatedBy(userService.getUserById(item.getCreatedById()));
            ItemByManufacturer ibm = item.getItemByManufacturer();
            if (ibm == null) {
                ibm = itemByManufacturerService.findOne(item.getItemByManufacturerId());
                item.setItemByManufacturer(ibm);
            }
            L_CodeCategory category = ibm.getLCodeCategory();
            if (category == null) {
                category = lCodeCategoryService.findOne(ibm.getLCodeCategoryId());
                ibm.setLCodeCategory(category);
            }
            String description = ibm.getDescription();
            if (!Strings.isNullOrEmpty(description) && description.contains("\"")) {
                item.setDescription(description.replaceAll("\\\"", "&quot;"));
            }
            Vendor manufacturer = ibm.getManufacturer();
            if (manufacturer == null) {
                manufacturer = vendorService.findOne(ibm.getManufacturerId());
                ibm.setManufacturer(manufacturer);
            }
        }
    }

    public Map<String, Object> saveForVersion(Item object) {
        Map<String, Object> response = null;
        // check for duplicates
        BigDecimal price = object.getPrice();
        List<Item> duplicates = itemRepository.findDuplicates(object.getItemByManufacturerId(), price, object.getVendorId());
        if (duplicates != null && !duplicates.isEmpty()) {
            Long objectId = object.getId();
            if ((objectId == null || objectId <= 0) || duplicates.stream().anyMatch(i -> !i.getId().equals(objectId))) {
                response = new HashMap<>(1);
                DecimalFormat fmt = new DecimalFormat("######0.00");
                Vendor vendor = object.getVendor();
                String vendorId = vendor != null ? vendor.getName() : object.getVendorId().toString();
                String error = String.format("There is already an Item \"%s\" for vendor \"%s\" with price %s",
                        object.getName(), vendorId, fmt.format(price));
                response.put("message", error);
            }
        }
        if (response == null) {
            // new object
            Long objectId = object.getId();
            if (objectId == null || objectId <= 0) {
                object.setCreatedAt(Timestamp.valueOf(LocalDateTime.now()));
            }
            Long userId = object.getCreatedById();
            if (userId == null || userId <= 0) {
                User user = object.getCreatedBy();
                if (user == null) {
                    user = userService.getCurrentUser();
                    object.setCreatedBy(user);
                }
                object.setCreatedById(user.getId());
            }

            // For unit testing
            TableObjectContainer.storeForJUnitTestingIfApplicationIsNotRunning(object);

            // save to the database
            response = super.saveForVersion(object, objectId);

            // loadForeignKeys()
            Object dbObject = response.get(SAVED);
            if (dbObject != null) {
                try {
                    Item dbItem = (Item)dbObject;
                    loadForeignKeys(dbItem);
                    response.put(SAVED, dbItem);
                } catch(Exception ex) {
                    // do nothing
                }
            }
        }
        return response;
    }
}
