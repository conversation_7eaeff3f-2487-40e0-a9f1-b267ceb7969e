package com.nymbl.tenant.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nymbl.config.Constants;
import com.nymbl.config.cascade.*;
import com.nymbl.config.cascade.order_complete.Note;
import com.nymbl.config.cascade.order_complete.*;
import com.nymbl.config.cascade.order_complete.response.Body;
import com.nymbl.config.cascade.poom.ItemIn;
import com.nymbl.config.cascade.poom.PunchOutOrderMessage;
import com.nymbl.config.cascade.poom.PunchOutOrderMessageHeader;
import com.nymbl.config.cascade.pos_request.Contact;
import com.nymbl.config.cascade.pos_request.*;
import com.nymbl.config.dto.CascadeAuthTokenDTO;
import com.nymbl.config.dto.cascade.ShippingAddressDTO;
import com.nymbl.config.model.FullName;
import com.nymbl.config.security.TokenUtils;
import com.nymbl.config.service.EmailServiceImpl;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.NumberUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.config.validation.interfaces.PoomValidation;
import com.nymbl.guestuser.enums.GuestUserAccessType;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.ThirdPartyShippingMethod;
import com.nymbl.master.model.User;
import com.nymbl.master.service.ThirdPartyShippingMethodService;
import com.nymbl.master.service.ThirdPartyWarehouseService;
import com.nymbl.master.service.UserService;
import com.nymbl.properties.CascadeProperties;
import com.nymbl.properties.RetailProperties;
import com.nymbl.properties.SPSProperties;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.*;
import jakarta.mail.MessagingException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.xml.Jaxb2RootElementHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestTemplate;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@Slf4j
@Service
public class RetailService {

    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    @Value("${base.url}")
    private String baseURL;

    @Value("${punchout.token.expiration}")
    private Long expiration;

    private final UserService userService;
    private final ItemService itemService;
    private final VendorService vendorService;
    private final PurchaseOrderService purchaseOrderService;
    private final PurchaseOrder_ItemService purchaseOrderItemService;
    private final ThirdPartyShippingMethodService thirdPartyShippingMethodService;
    private final ThirdPartyWarehouseService thirdPartyWarehouseService;
    private final TokenUtils tokenUtils;
    private final CascadeProperties cascadeProperties;
    private final SPSProperties spsProperties;
    private final EmailServiceImpl emailService;
    private final PatientService patientService;
    private final PrescriptionService prescriptionService;

    private final ItemByManufacturerService itemByManufacturerService;

    @Autowired
    public RetailService(UserService userService,
                         ItemService itemService,
                         VendorService vendorService,
                         PurchaseOrderService purchaseOrderService,
                         PurchaseOrder_ItemService purchaseOrderItemService,
                         ThirdPartyShippingMethodService thirdPartyShippingMethodService,
                         ThirdPartyWarehouseService thirdPartyWarehouseService,
                         TokenUtils tokenUtils,
                         CascadeProperties cascadeProperties,
                         SPSProperties spsProperties,
                         EmailServiceImpl emailService,
                         PatientService patientService,
                         PrescriptionService prescriptionService,
                         ItemByManufacturerService itemByManufacturerService) {
        this.userService = userService;
        this.itemService = itemService;
        this.vendorService = vendorService;
        this.purchaseOrderService = purchaseOrderService;
        this.purchaseOrderItemService = purchaseOrderItemService;
        this.thirdPartyShippingMethodService = thirdPartyShippingMethodService;
        this.thirdPartyWarehouseService = thirdPartyWarehouseService;
        this.tokenUtils = tokenUtils;
        this.cascadeProperties = cascadeProperties;
        this.spsProperties = spsProperties;
        this.emailService = emailService;
        this.patientService = patientService;
        this.prescriptionService = prescriptionService;
        this.itemByManufacturerService = itemByManufacturerService;
    }

    public CXML punchOut(Long patientId, Long prescriptionId, String vendorName, Long branchId) throws Exception {
        String vendor;
        RetailProperties retailProperties;
        if (vendorName.equalsIgnoreCase("cascade")) {
            vendor = Constants.CASCADE;
            retailProperties = cascadeProperties;
        } else {
            vendor = Constants.SPS;
            retailProperties = spsProperties;
        }
        User user = userService.getCurrentUser();
        CXML request = buildCXMLLandingRequest(user, patientId, prescriptionId, vendor, retailProperties, branchId);
        //log.info("Login Request: \n" + xmlAsString(request));
        CXML response;
        String poStartUrl = retailProperties.getPoStartUrl();
        if (StringUtil.isBlank(poStartUrl)) {
            throw new Exception();
        }
        try {
            RestClient restClient = RestClient.builder()
                    .requestFactory(new HttpComponentsClientHttpRequestFactory())
                    .messageConverters(converters -> converters.add(new Jaxb2RootElementHttpMessageConverter()))
                    .build();

            ResponseEntity<?> responseEntity = restClient.post()
                    .uri(poStartUrl)
                    .contentType(MediaType.APPLICATION_XML)
                    .body(xmlAsString(request))
                    .retrieve()
                    .toEntity(String.class);
            String xml = (String) responseEntity.getBody();
            // NC-225: xml = xml.substring(xml.indexOf('<')); // this removed the BOM, if present
            //log.info("Login Response: \n" + xml);
            assert xml != null;
            InputStream is = new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8));
            response = cXML(null, is);
        } catch (HttpClientErrorException e) {
            String error = "Error occurred while opening (".concat(poStartUrl).concat(") with (POST) ").concat(e.getResponseBodyAsString());
            log.error(error);
            throw new Exception(error);
        }
        return response;
    }

    public PurchaseOrder savePoom(String xml) throws Exception {
        PurchaseOrder po;
        try {
            po = convertPOOMtoPurchaseOrder(getCxml(xml), xml);
        } catch (Exception e) {
            log.error("\n".concat(xml));
            throw e;
        }
        return po;
    }

    @SuppressWarnings("unchecked")
    public CXML getCxml(String xml) throws Exception {
        CXML cXML = cXML(null, IOUtils.toInputStream(xml, StandardCharsets.UTF_8));
        Set<ConstraintViolation<PunchOutOrderMessage>> constraintViolations =
                validator.validate(cXML.getMessage().getPunchOutOrderMessage(), PoomValidation.class);
        if (!constraintViolations.isEmpty()) {
            List<String> details = constraintViolations.parallelStream().map
                    (ConstraintViolation::getMessage).toList();
            JSONArray array = new JSONArray();
            array.addAll(details);
            log.info("\n".concat(xml));
            log.error(String.valueOf(array));
            throw new Exception(String.valueOf(array));
        }
        return cXML;
    }

    private CXML buildCXMLLandingRequest(User user, Long patientId, Long prescriptionId, String vendorName, RetailProperties retailProperties, Long branchId) {
        CXML cxml = new CXML();
        //datetime.process id.random number@hostname
        String datetime = DateUtil.getStringDate(DateUtil.getCurrentDate(), Constants.DF_YYYYMMDDHHmmssSSS);
        String processId = "P".concat(String.valueOf(patientId)).concat("RX").concat(String.valueOf(prescriptionId));
        String randomNumber = String.valueOf(user.getId());
        String hostname = user.getEmail().replace('@', '.');
        Long companyId = userService.getCurrentCompany().getId();
        String buyerCookie = tokenUtils.generateGuestToken(user.getUsername(), "punchOut", user.getId(), companyId, branchId, expiration, GuestUserAccessType.PUNCHOUT.name());
        cxml.setPayloadID(datetime.concat(".").concat(getBuyerCookieHash(buyerCookie)).concat(".").concat(processId).concat(".").concat(randomNumber).concat("@").concat(hostname));
        java.util.Date tempDate = new java.util.Date();
        cxml.setTimestamp(new Timestamp(tempDate.getTime()).toString());
        if ("COS".equals(retailProperties.getCompany())) {
            cxml.setHeader(buildHeader(
                            new Credential("DUNS", "DUNS", null),
                            new Credential("DUNS", "DUNS", null),
                            new Credential("DUNS", user.getEmail(), user.getCascadePassword())
// NC-225:                          new Credential("", "", null),
// NC-225:                          new Credential("", "", null),
// NC-225:                          new Credential("nymbl", user.getEmail(), user.getCascadePassword())
                    )
            );
        } else if ("spsco_store_view".equals(retailProperties.getCompany())) {
            cxml.setHeader(buildHeader(
                            new Credential("NetworkID", "nymbl", null),
                            new Credential("NetworkID", "hanger", "32SG9wGxz5"),
                            new Credential("NetworkID", user.getEmail(), "")
                    )
            );
        }
        Vendor v = vendorService.findByName(vendorName);
        String vendorAccountId;
        if ("COS".equals(retailProperties.getCompany())) {
            vendorAccountId = !StringUtil.isBlank(user.getCascadeAccountId()) ? user.getCascadeAccountId() : v.getVendorCustomerId();
        } else {
            vendorAccountId = v.getVendorCustomerId();
        }
        FullName fullName = new FullName(user.getFirstName(), user.getMiddleName(), user.getLastName());
        List<Extrinsic> extrinsicList = new ArrayList<>();
        extrinsicList.add(new Extrinsic("FirstName", user.getFirstName()));
        extrinsicList.add(new Extrinsic("LastName", user.getLastName()));
        extrinsicList.add(new Extrinsic("UniqueName", user.getEmail()));
        extrinsicList.add(new Extrinsic("UserPrintableName", StringUtil.formatName(fullName, "FL", false)));
        extrinsicList.add(new Extrinsic("UserOrgName", user.getCompany().getName()));
        extrinsicList.add(new Extrinsic("UserOrgId", vendorAccountId));
        extrinsicList.add(new Extrinsic("UserOrgDept", ""));
        extrinsicList.add(new Extrinsic("UserDept", ""));
        extrinsicList.add(new Extrinsic("UniqueUsername", user.getEmail()));
        extrinsicList.add(new Extrinsic("ReturnFrame", "_self"));
        extrinsicList.add(new Extrinsic("UserEmail", user.getEmail()));
        extrinsicList.add(new Extrinsic("PhoneNumber", user.getPhoneNumber()));
        extrinsicList.add(new Extrinsic("User", user.getEmail()));
        extrinsicList.add(new Extrinsic("Patient ID", patientId != null ? patientId.toString() : ""));
        extrinsicList.add(new Extrinsic("Prescription ID", prescriptionId != null ? prescriptionId.toString() : ""));

        if (patientId != null) {
            extrinsicList.add(new Extrinsic("ReturnURL", baseURL.concat("/#/app/patient/profile/").concat(patientId.toString())));
        } else {
            extrinsicList.add(new Extrinsic("ReturnURL", baseURL.concat("/#/app/purchasing/all")));
        }

        BrowserFormPost browserFormPost = new BrowserFormPost(baseURL.concat("/retail/web/poom"));
        Contact contact = new Contact(new Name(StringUtil.formatName(fullName, "FL", false)), user.getEmail());
        SupplierSetup supplierSetup = new SupplierSetup(retailProperties.getPoLoginUrl());

        //At this point Address etc... is irrelevant still not completely sure why they do not provide this back in the POOM where it is relevant.
        PostalAddress postalAddress = new PostalAddress();
        postalAddress.setStreet("");
        postalAddress.setCity("");
        postalAddress.setState(null);
        postalAddress.setCountry(new Country("", ""));
        Phone phone = new Phone(new TelephoneNumber(new Country("", ""), "", ""));
        Address address = new Address();
        address.setAddressID("PunchOut User");
        address.setName(new Name(""));
        address.setPhone(phone);
        address.setPostalAddress(postalAddress);
        ShipTo shipTo = new ShipTo(address);
        cxml.setRequest(buildPunchOutRequest(extrinsicList, browserFormPost, contact, supplierSetup, shipTo, buyerCookie));
        return cxml;
    }

    private String getBuyerCookieHash(String buyerCookie) {
    	try {
    		MessageDigest digest = MessageDigest.getInstance("SHA-256");
    		return Base64.getEncoder().encodeToString(digest.digest(buyerCookie.getBytes(StandardCharsets.UTF_8)));
    	}
    	catch (NoSuchAlgorithmException e) {
    		return Long.toHexString(System.currentTimeMillis());
    	}
    }

    private Header buildHeader(Credential credential1, Credential credential2, Credential credential3) {
        Header header = new Header();
        header.setFrom(new From(credential1));
        header.setTo(new To(credential2));
        header.setSender(new Sender(credential3, "Nymbl"));
        return header;
    }

    private Request buildPunchOutRequest(List<Extrinsic> extrinsicList, BrowserFormPost browserFormPost, Contact contact, SupplierSetup supplierSetup, ShipTo shipTo, String buyerCookie) {
        PunchOutSetupRequest posr = new PunchOutSetupRequest();
        posr.setOperation("create");
        posr.setBuyerCookie(buyerCookie);
        posr.setExtrinsic(extrinsicList);
        posr.setBrowserFormPost(browserFormPost);
        posr.setContact(contact);
        posr.setSupplierSetup(supplierSetup);
        posr.setShipTo(shipTo);

        Request request = new Request();
        request.setPunchOutSetupRequest(posr);
        return request;
    }

    public CascadeAuthTokenDTO getCascadeAuthToken() {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> map =
                new LinkedMultiValueMap<>();
        map.add("username", cascadeProperties.getApiUsername());
        map.add("password", cascadeProperties.getApiPassword());
        map.add("grant_type", cascadeProperties.getApiGrantType());
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        ResponseEntity<CascadeAuthTokenDTO> response = restTemplate.exchange(cascadeProperties.getApiUrl().concat("/api/authorization/token"), HttpMethod.POST, entity, CascadeAuthTokenDTO.class);
        return response.getBody();
    }

    public ResponseEntity<?> getCascadeDeliveryAddressByCustomerID(String acctNum) {
        CascadeAuthTokenDTO token = getCascadeAuthToken();
        Vendor vendor = vendorService.findByName(Constants.CASCADE);
        String accountNumber = acctNum != null ? acctNum : vendor.getVendorCustomerId();
        String tempUrl = cascadeProperties.getApiUrl().concat("/api/address/getAddressRequest?CustomerId=" + accountNumber + "&CompanyId=" + cascadeProperties.getCompany());

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + token.getAccess_token());
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        try {
            ResponseEntity<List<ShippingAddressDTO>> response =
                    restTemplate.exchange(tempUrl,
                            HttpMethod.GET,
                            entity,
                            new ParameterizedTypeReference<>() {
                            });
            return response;
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    private PurchaseOrder convertPOOMtoPurchaseOrder(CXML cxml, String xml) {
        boolean isCascade = isCascade(cxml);
        Map<String, String> map = thirdPartyWarehouseService.loadWarehouseMap(isCascade ? "COS" : "");

        String token = cxml.getMessage().getPunchOutOrderMessage().getBuyerCookie();
        Company co = tokenUtils.getCompanyFromToken(token);
        User user = userService.findOne(tokenUtils.getUserIdFromToken(token));
        TenantContext.setCurrentTenant(co.getKey());
        Branch br = tokenUtils.getBranchFromToken(token);

        PunchOutOrderMessage poom = cxml.getMessage().getPunchOutOrderMessage();
        Vendor vendor = vendorService.findByName(isCascade ? Constants.CASCADE : Constants.SPS);
        RetailProperties retailProperties = isCascade ? cascadeProperties : spsProperties;

        PurchaseOrder result = new PurchaseOrder();
        result.setOrderedById(user.getId());
        result.setOrderedBy(user);
        result.setStatus("open_cart");
        result.setVendor(vendor);
        result.setVendorId(vendor.getId());
        BigDecimal tax = BigDecimal.ZERO;
        PunchOutOrderMessageHeader punchOutOrderMessageHeader = poom.getPunchOutOrderMessageHeader();
        BigDecimal subTotal = BigDecimal.ZERO;
        for (ItemIn x : poom.getItemIn()) {
            BigDecimal quantity = new BigDecimal(x.getQuantity());
            BigDecimal price = BigDecimal.valueOf(Double.parseDouble(x.getItemDetail().getUnitPrice().getMoney().getAmount()));
            BigDecimal subPrice = price.multiply(quantity);
            subTotal = subTotal.add(subPrice);
        }
        if (punchOutOrderMessageHeader.getTax() != null &&
                punchOutOrderMessageHeader.getTax().getMoney() != null &&
                StringUtils.isNotBlank(punchOutOrderMessageHeader.getTax().getMoney().getAmount())) {
            tax = new BigDecimal(punchOutOrderMessageHeader.getTax().getMoney().getAmount());
        }
        result.setSalesTax(tax);
        BigDecimal shipping = BigDecimal.ZERO;
        if (punchOutOrderMessageHeader.getShipping() != null) {
            shipping = new BigDecimal(punchOutOrderMessageHeader.getShipping().getMoney().getAmount());
        }
        if (punchOutOrderMessageHeader.getExtrinsics() != null) {
            for (Extrinsic extrinsic : punchOutOrderMessageHeader.getExtrinsics()) {
                if ("Discount".equals(extrinsic.getName())) {
                    result.setDiscount(NumberUtil.stringAmountToBigDecimal(extrinsic.getContent()).abs());
                }

                if (!isCascade) {
                    switch (extrinsic.getName()) {
                        case "handling_fee":
                            result.setAdditionalCharges(NumberUtil.stringAmountToBigDecimal(extrinsic.getContent()));
                            break;
                        case "handling_fee_description":
                            if (StringUtils.isNotBlank(extrinsic.getContent())) {
                                result.setNote("Add. Charges -> ".concat(extrinsic.getContent()));
                            }
                            break;
                        case "shipping_method":
                            break;
                        case "shipping_code":
                            break;
                        case "street":
                            result.setStreetAddress(extrinsic.getContent());
                            break;
                        case "city":
                            result.setCity(extrinsic.getContent());
                            break;
                        case "region":
                            result.setState(StringUtil.getStateAbbr(extrinsic.getContent()));
                            break;
                        case "postcode":
                            result.setZipcode(extrinsic.getContent());
                            break;
                        case "country_id":
                            result.setCountry(extrinsic.getContent());
                            break;
                    }
                }
            }
        }
        result.setShippingCharges(shipping);
        result.setSubTotal(subTotal);
        BigDecimal discount = result.getDiscount() != null ? result.getDiscount() : BigDecimal.ZERO;
        BigDecimal t = tax.add(shipping).subtract(discount);
        result.setTotalCost(subTotal.add(t));
        result.setManualStatusSelection(true);
        result.setOrderedAt(Date.valueOf(DateUtil.getLocalDateTimeWithUserTimezoneId().toLocalDate()));
        if (!isCascade) {
            ThirdPartyShippingMethod thirdPartyShippingMethod = thirdPartyShippingMethodService.findByVendorAndVendorShippingCode("SPS", "DEFAULT");
            result.setThirdPartyShippingMethodId(thirdPartyShippingMethod.getId());
        }
        result.setPoom(xml);
        result = purchaseOrderService.save(result);

        for (ItemIn x : poom.getItemIn()) {
            Item item = itemService.findByPartNumberAndVendorId(x.getItemID().getSupplierPartID(), vendor.getId());
            String description = StringUtil.correctMalformedXML(x.getItemDetail().getDescription().getValue());
            if (!isCascade) description = getName(description);
            if (item == null) {
                item = new Item();
                ItemByManufacturer ibm = itemByManufacturerService.findOrCreate(
                        description, vendor.getId(), x.getItemID().getSupplierPartID(), null, null);
                item.setItemByManufacturer(ibm);
                item.setItemByManufacturerId(ibm.getId());
                item.setVendorId(vendor.getId());
                item.setSku(x.getItemID().getSupplierPartID());
                item.setPartNumber(x.getItemID().getSupplierPartID());
                item.setPrice(BigDecimal.valueOf(Double.parseDouble(x.getItemDetail().getUnitPrice().getMoney().getAmount())));
                item.setActive(true);
                if (item.getCreatedById() == null) {
                    item.setCreatedById(user.getId());
                }
                item = itemService.save(item);
            }

            PurchaseOrder_Item poItem = new PurchaseOrder_Item();
            poItem.setStatus("open_cart");
            poItem.setPurchaseOrderId(result.getId());
            if (br != null) {
                poItem.setBranchId(br.getId());
            }
            poItem.setItemId(item.getId());
            poItem.setItemCost(item.getPrice());
            poItem.setQuantity(Long.valueOf(x.getQuantity()));
            poItem.setVendorId(vendor.getId());
            StringBuilder sb = new StringBuilder();
            if (x.getItemDetail().getExtrinsic() != null) {
                for (Extrinsic e : x.getItemDetail().getExtrinsic()) {
                    if (e.getName().equalsIgnoreCase("Patient ID")) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            Long patientId;
                            try {
                                patientId = Long.valueOf(e.getContent());
                                Patient p = patientService.findOne(patientId);
                                if (p != null) {
                                    poItem.setPatientId(patientId);
                                    sb.append("--Patient Id: ".concat(patientId.toString()));
                                }
                            } catch (NumberFormatException nfe) {
                                log.error("Patient ID is not a number : ".concat(e.getContent()));
                                sb.append("--Patient Id: ".concat(e.getContent()));
                            }
                        }
                    } else if (e.getName().equalsIgnoreCase("Prescription ID")) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            Long prescriptionId;
                            try {
                                prescriptionId = Long.valueOf(e.getContent());
                                Prescription rx = prescriptionService.findOne(prescriptionId);
                                if (rx != null) {
                                    poItem.setPrescriptionId(prescriptionId);
                                    sb.append("--Prescription Id: ".concat(prescriptionId.toString()));
                                }
                            } catch (NumberFormatException nfe) {
                                log.error("Prescription ID is not a number : ".concat(e.getContent()));
                                sb.append("--Prescription Id: ".concat(e.getContent()));
                            }
                        }
                    } else if (e.getName().equalsIgnoreCase("note")) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            sb.append("--Note: ".concat(e.getContent()));
                        }
                    } else if (e.getName().equalsIgnoreCase("warehouse")) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            poItem.setThirdPartyLocationId(e.getContent());
                            sb.append("--Warehouse: ".concat(map.get(e.getContent())));
                        } else {
                            sb.append("--Warehouse: Vendor Direct");
                            poItem.setThirdPartyLocationId(null);
                        }
                    } else if (e.getName().equalsIgnoreCase("shipmethod")) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            ThirdPartyShippingMethod thirdPartyShippingMethod = thirdPartyShippingMethodService.findByVendorAndVendorShippingCode(retailProperties.getCompany(), e.getContent());
                            poItem.setThirdPartyShippingMethodId(thirdPartyShippingMethod.getId());
                            sb.append("--Shipping Method: ".concat(thirdPartyShippingMethod.getVendorShippingMethod()));
                        }
                    }
                }
            }
            if (isCascade) {
                poItem.setAdditionalComments(sb.toString());
            } else {
                poItem.setAdditionalComments(description.substring(description.indexOf(";") + 2));
            }
            purchaseOrderItemService.save(poItem);
        }
        return result;
    }

    public HttpHeaders buildHttpHeader(boolean isCascade) {
        HttpHeaders result = new HttpHeaders();
        result.setContentType(MediaType.APPLICATION_JSON);
        if (isCascade) {
            CascadeAuthTokenDTO token = getCascadeAuthToken();
            result.set("Authorization", "Bearer " + token.getAccess_token());
        }
        return result;
    }

    public ResponseEntity<?> completePurchase(Long purchaseOrderId, Long userId) {
        StringBuilder cascadeEmailBody = new StringBuilder();
//        sb.append("<style>tr:nth-child(even) {background-color: #f2f2f2;}</style>");
        ResponseEntity<?> responseEntity = null;
        Body body = null;
        RetailProperties retailProperties;
        PurchaseOrder po = purchaseOrderService.findOne(purchaseOrderId);
        List<PurchaseOrder_Item> poItems = purchaseOrderItemService.findByPurchaseOrderId(po.getId());
        String purchaseOrderPrefix = "";
        if (!poItems.isEmpty()) {
            PurchaseOrder_Item firstItem = poItems.get(0);
            if (firstItem.getBranch() != null && firstItem.getBranch().getPurchaseOrderPrefix() != null) {
                purchaseOrderPrefix = firstItem.getBranch().getPurchaseOrderPrefix();
            }
        }
        try {
            boolean isCascade = isCascade(getCxml(po.getPoom()));
            Map<String, String> map = thirdPartyWarehouseService.loadWarehouseMap(isCascade ? "COS" : "SPS");
            retailProperties = isCascade ? cascadeProperties : spsProperties;
            if (purchaseOrderPrefix.isEmpty()) {
                po.setWebReference(TenantContext.getCurrentTenant().concat("-").concat(po.getId().toString()));
            } else {
                po.setWebReference(TenantContext.getCurrentTenant().concat("-").concat(purchaseOrderPrefix).concat(po.getId().toString()));
            }
            OrderRequest orderRequest = buildOrderRequest(po, poItems, isCascade);
            if (isCascade) {
                cascadeEmailBody.append("<table>");
                cascadeEmailBody.append("<tr><th colspan=\"2\">ORDER SUMMARY</th></tr>");
                cascadeEmailBody.append("<tr><td style=\"width: 500px;\">SHIP TO</td><td>ORDER NUMBER</td></tr>");
            }
            //log.info("Order Complete Request:\n" + new ObjectMapper().writeValueAsString(orderRequest));
            HttpHeaders headers = buildHttpHeader(isCascade);
            String url = isCascade ? retailProperties.getApiUrl().concat("/api/order/create") : retailProperties.getApiUrl();
            //log.info("COMPLETE ORDER URL: " + url);
            responseEntity = new RestTemplate().exchange(url, HttpMethod.POST, new HttpEntity<>(orderRequest, headers), String.class);
            //log.info("Order Complete Response Entity:\n" + responseEntity);
            String refNo;
            if (isCascade) {
                body = new ObjectMapper().readValue(Objects.requireNonNull(responseEntity.getBody()).toString(), Body.class);
                //log.info("Order Complete Response:\n" + new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(body));
                refNo = body.getOrderNo();
            } else {
                //log.info("Order Complete Response:\n" + responseEntity.getBody().toString());
                refNo = Objects.requireNonNull(responseEntity.getBody()).toString();
            }
            po.setStatus("ordered");
            po.setOrderedById(userId);
            po.setReferenceNumber(refNo);
            purchaseOrderService.save(po);

            if (isCascade) {
                cascadeEmailBody.append("<tr><td style=\"vertical-align: top;\">");
                cascadeEmailBody.append(orderRequest.getShipToName()).append("<br>");
                cascadeEmailBody.append(orderRequest.getShipToAddress1()).append("<br>");
                if (!StringUtil.isBlank(orderRequest.getShipToAddress2())) {
                    cascadeEmailBody.append(orderRequest.getShipToAddress2()).append("<br>");
                }
                cascadeEmailBody.append(orderRequest.getShipToCity())
                        .append(" ")
                        .append(body.getOeHdrShip2State())
                        .append(", ")
                        .append(body.getZipCode()).append(", ").append(body.getShipToCountry()).append("<br>");
                cascadeEmailBody.append("T: ").append(body.getShipToPhone());
                cascadeEmailBody.append("</td>");
                cascadeEmailBody.append("<td>");
                cascadeEmailBody.append(body.getOrderNo()).append("<br><br>");
                if (purchaseOrderPrefix.isEmpty()) {
                    cascadeEmailBody.append("PO NUMBER").append("<br>").append(po.getId()).append("<br><br>");
                } else {
                    cascadeEmailBody.append("PO NUMBER").append("<br>").append(purchaseOrderPrefix).append(po.getId()).append("<br><br>");
                }
//            Date d = new Date(Long.parseLong(body.getOrderDate()) * 1000);
                cascadeEmailBody.append("ORDER DATE").append("<br>").append(DateUtil.getStringDate(po.getOrderedAt(), "MM/dd/yyyy HH:mm a"));
                cascadeEmailBody.append("</td>");
                cascadeEmailBody.append("</tr>");
                cascadeEmailBody.append("</table><p></p>");
                cascadeEmailBody.append("<table>");
                cascadeEmailBody.append("<tr><th style=\"text-align: left; width: 500px;\">ITEM</th>")
                        .append("<th style=\"text-align: right;\">QTY</th>")
                        .append("<th style=\"text-align: right;\">PRICE</th>")
                        .append("<th style=\"text-align: right;\">TOTAL</th></tr>");
            }
            int lineNo = 0;
            BigDecimal total = BigDecimal.ZERO;
            for (PurchaseOrder_Item poi : poItems) {
                poi.setStatus("ordered");
                purchaseOrderItemService.save(poi);
                Item i = poi.getItem();
                int mod = lineNo % 2;
                if (isCascade) {
                    if (mod == 0)
                        cascadeEmailBody.append("<tr>");
                    else
                        cascadeEmailBody.append("<tr style=\"background-color: #f2f2f2;\">");
                    cascadeEmailBody.append("<td>");
                    cascadeEmailBody.append("<strong>").append(i.getName()).append("</strong><br>");
                    cascadeEmailBody.append("Item ID: ").append(i.getPartNumber()).append("<br>");
                    cascadeEmailBody.append("Warehouse: ").append(map.get(poi.getThirdPartyLocationId())).append("<br>");
                    cascadeEmailBody.append("Shipping Method: ").append(poi.getThirdPartyShippingMethod().getVendorShippingMethod());
                    cascadeEmailBody.append("</td>");
                    cascadeEmailBody.append("<td style=\"text-align: right;\">").append(poi.getQuantity()).append("</td>");
                    cascadeEmailBody.append("<td style=\"text-align: right;\">$").append(i.getPrice()).append("</td>");
                    total = total.add(i.getPrice().multiply(BigDecimal.valueOf(poi.getQuantity())));
                    cascadeEmailBody.append("<td style=\"text-align: right;\">$").append(i.getPrice().multiply(BigDecimal.valueOf(poi.getQuantity()))).append("</td>");
                    cascadeEmailBody.append("</tr>");
                    cascadeEmailBody.append("<tr><td colspan=\"4\"></td></tr>");
                }
                lineNo++;
            }
            if (isCascade) {
                cascadeEmailBody.append("<tr><td colspan=\"2\"></td>")
                        .append("<td style=\"text-align: right;\">Shipping:</td><td style=\"text-align: right;\">Invoiced</td></tr>");
                cascadeEmailBody.append("<tr><td></td><td></td>")
                        .append("<td style=\"text-align: right;\">Total:</td><td style=\"text-align: right;\">$")
                        .append(total).append("</td></tr>");
                cascadeEmailBody.append("</table>");
//            FileUtils.writeStringToFile(new File("./src/test/resources/cascade_email.html"), sb.toString());
                try {
                    emailService.sendCompletedCascadeOrder(cascadeEmailBody.toString(), body.getOrderNo());
                } catch (MessagingException e) {
                    log.warn("Error sending email to Cascade.");
                }
            }
            return ResponseEntity.ok(isCascade ? body : null);
        } catch (Exception e) {
            po.setStatus("purchase_order_error");
            purchaseOrderService.save(po);
            for (PurchaseOrder_Item poi : poItems) {
                poi.setStatus("purchase_order_error");
                purchaseOrderItemService.save(poi);
            }
            String exceptionAsString = StringUtil.getExceptionAsString(e);
            log.error(TenantContext.getCurrentTenant().concat(" Error completing order (response): ").concat(String.valueOf(responseEntity)).concat("\n(error)").concat(exceptionAsString));
            return ResponseEntity.badRequest().body(exceptionAsString);
        }
    }

    public OrderRequest buildOrderRequest(PurchaseOrder po, List<PurchaseOrder_Item> poItems, boolean isCascade) {
        CXML cxml = cXML(null, IOUtils.toInputStream(po.getPoom(), StandardCharsets.UTF_8));
        PunchOutOrderMessage poom = cxml.getMessage().getPunchOutOrderMessage();

        RetailProperties retailProperties = isCascade ? cascadeProperties : spsProperties;
        Vendor vendor = vendorService.findByName(isCascade ? Constants.CASCADE : Constants.SPS);
        User tempUser = userService.getCurrentUser();
        FullName tempName = new FullName(tempUser.getFirstName(), tempUser.getMiddleName(), tempUser.getLastName());

        OrderRequest result = new OrderRequest();
        result.setCustomerId(vendor.getVendorCustomerId());
        result.setCompanyId(retailProperties.getCompany());
        if (isCascade) {
            result.setLocationId(Integer.parseInt(po.getLocationId()));
        }
        String purchaseOrderPrefix = "";
        if (!poItems.isEmpty()) {
            PurchaseOrder_Item firstItem = poItems.get(0);
            if (firstItem.getBranch() != null && firstItem.getBranch().getPurchaseOrderPrefix() != null) {
                purchaseOrderPrefix = firstItem.getBranch().getPurchaseOrderPrefix();
            }
        }
        if (purchaseOrderPrefix.isEmpty()) {
            result.setPoNo(po.getId().toString());
        } else {
            result.setPoNo(purchaseOrderPrefix.concat(po.getId().toString()));
        }
        result.setSalesTax(po.getSalesTax() == null ? "0.00" : po.getSalesTax().toString());
        result.setTaker(retailProperties.getTaker());
        if (!isCascade) {
            List<Extrinsic> extrinsics = poom.getPunchOutOrderMessageHeader().getExtrinsics();
            Optional<Extrinsic> siteName = extrinsics.stream().filter(e -> e.getName().equalsIgnoreCase("site_name")).findFirst();
            result.setShipToId(siteName.map(Extrinsic::getContent).orElse(null));
        } else {
            result.setShipToId(String.valueOf(po.getThirdPartyLocationId()));
        }
        String name = isCascade ? po.getAttn() : StringUtil.toUpperCaseSafe(po.getAttn());
        String address1 = isCascade ? po.getStreetAddress() : StringUtil.toUpperCaseSafe(po.getStreetAddress());
        String city = isCascade ? po.getCity() : StringUtil.toUpperCaseSafe(po.getCity());
        String email = isCascade && po.getOrderedBy() != null ? po.getOrderedBy().getEmail() : po.getOrderedBy() != null ? StringUtil.toUpperCaseSafe(po.getOrderedBy().getEmail()) : "";
        String state = isCascade ? po.getState() : StringUtil.toUpperCaseSafe(po.getState());
        String zipcode = isCascade ? po.getZipcode() : StringUtil.toUpperCaseSafe(po.getZipcode());
        String country = isCascade ? po.getCountry() : StringUtil.toUpperCaseSafe(po.getCountry());

        result.setShipToName(name);
        result.setShipToAddress1(address1);
        result.setShipToCity(city);
        result.setShip2MailAddress(email);
        result.setOeHdrShip2State(state);
        result.setZipCode(zipcode);
        result.setShipToCountry(country);
        if (!isCascade) {
            result.setBillToName(name);
            result.setBillToAddress1(address1);
            result.setBillToCity(city);
            result.setBillToState(state);
            result.setBillToZipCode(zipcode);
            result.setBillToCountry(country);
            result.setBill2MailAddress(email);
        }
        if (!isCascade) {
            result.setShippingMethod(po.getThirdPartyShippingMethod().getVendorShippingCode());
        }
        result.setWillCall("N");
        result.setClass1id("NYMBL");
        result.setShipToPhone(po.getPhoneNumber());
        result.setPlacedByName(StringUtil.formatName(tempName, "FL", false));
        result.setJobName(StringUtil.formatName(tempName, "FL", false));
        result.setRma(null);
        result.setWebReferenceNo(po.getWebReference());

        Notes hdrNotes = new Notes();
        Note hdrNote = new Note();
        hdrNote.setNote(po.getNote());
        hdrNote.setTopic("Customer Note");
        hdrNote.setMandatory(true);
        hdrNote.setLineNo(1);
        hdrNotes.setList(Collections.singletonList(hdrNote));
        result.setNotes(hdrNotes);

        ArrayList<Line> allLines = new ArrayList<>();
        Lines lines = new Lines();
        int lineNumber = 1;
        for (PurchaseOrder_Item poi : poItems) {
            ItemIn x = poom.getItemIn().get(lineNumber - 1);
            Line line = new Line();
            Map<String, Object> userDefinedFields = new HashMap<>();
            line.setUserDefinedFields(userDefinedFields);
            line.setItemId(x.getItemID().getSupplierPartID());
            line.setUnitQuantity(Long.parseLong(x.getQuantity()));
            line.setUnitOfMeasure(x.getItemDetail().getUnitOfMeasure());
            line.setPricingUnit(x.getItemDetail().getUnitPrice().getMoney().toString());
            line.setWillCall("N");
            line.setManualPriceOverride("N");//SIC misspelling
            line.setLineNo(lineNumber);

            List<Extrinsic> extrinsics = x.getItemDetail().getExtrinsic();
            if (extrinsics != null) {
                for (Extrinsic e : extrinsics) {
                    if (e.getName().equalsIgnoreCase("warehouse")) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            line.setSourceLocId(Long.parseLong(e.getContent()));
                        } else {
                            line.setDisposition("D");
                            line.setSourceLocId(null);
                        }
                    } else if (e.getName().equalsIgnoreCase("shipmethod") && isCascade) {
                        if (!StringUtil.isBlank(e.getContent())) {
                            line.getUserDefinedFields().put("CarrierIdTemp", Long.parseLong(e.getContent()));
                        }
                    }
                }
            }
            if (!isCascade) {
                line.getUserDefinedFields().put("SupplierPartAuxiliaryID", x.getItemID().getSupplierPartAuxiliaryID());
                line.getUserDefinedFields().put("Description", String.join("\n", x.getItemDetail().getDescription().getValue()));
                line.getUserDefinedFields().put("CarrierIdTemp", po.getThirdPartyShippingMethod().getVendorShippingCode());
            }

            Notes notes = new Notes();
            Note note = new Note();
            note.setNote(poi.getAdditionalComments());
            note.setTopic("Customer Note");
            note.setLineNo(lineNumber++);
            note.setMandatory(true);
            notes.setList(Collections.singletonList(note));
            line.setNotes(notes);

            allLines.add(line);
        }
        lines.setList(allLines);
        result.setLines(lines);
        return result;
    }

    public CXML cXML(JAXBContext jc, InputStream is) {
        CXML result = null;
        JAXBContext jaxbContext = jc;
        try {
            if (jaxbContext == null) {
                jaxbContext = JAXBContext.newInstance(CXML.class);
            }
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            result = (CXML) unmarshaller.unmarshal(is);
            is.close();
            return result;
        } catch (IOException | JAXBException e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        return result;
    }

    private String xmlAsString(CXML cxml) {
        try {
            StringWriter sw = new StringWriter();
            JAXBContext jbc = JAXBContext.newInstance(CXML.class);
            Marshaller jbm = jbc.createMarshaller();
            jbm.marshal(cxml, sw);
            return sw.toString();
        } catch (JAXBException e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        return null;
    }

    private Boolean isCascade(CXML cxml) {
// NC-225:        return !cxml.getHeader().getFrom().getCredential().getIdentity().equals("hanger");

        return "DUNS".equals(cxml.getHeader().getFrom().getCredential().getDomain()) &&
                "DUNS".equals(cxml.getHeader().getTo().getCredential().getDomain());
    }

    public String getName(String description) {
        for (String s : description.split(";")) {
            return s;
        }
        return "";
    }
}
