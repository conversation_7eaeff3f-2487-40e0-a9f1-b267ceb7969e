package com.nymbl.master.service;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.services.quicksight.AmazonQuickSight;
import com.amazonaws.services.quicksight.AmazonQuickSightClientBuilder;
import com.amazonaws.services.quicksight.model.*;
import com.nymbl.config.dto.ClericalProductivityReportDTO;
import com.nymbl.config.dto.UserDTO;
import com.nymbl.config.security.SecurityUtils;
import com.nymbl.config.security.ValidatePasswordHistory;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.master.model.User;
import com.nymbl.master.model.*;
import com.nymbl.master.repository.UserRepository;
import com.nymbl.tenant.ErrorMessage;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.batch.dto.QuicksightAssetUserPermissions;
import com.nymbl.tenant.batch.dto.QuicksightTenantUserPermission;
import com.nymbl.tenant.model.UserRole;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.*;
import com.nymbl.tenant.service.CalendarTemplateService;
import com.nymbl.tenant.service.QuickSightAccess;
import com.nymbl.tenant.service.SystemSettingService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.PersistenceContextType;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.nymbl.config.security.PasswordHistoryService;

/**
 * Created by Bradley Moore on 05/16/2017.
 */
@Slf4j
@Service
@Primary
public class UserService extends AbstractTableService<User, Long> implements UserDetailsService {

    private static final Logger log = LoggerFactory.getLogger(UserService.class);

    private final AuthenticationManager authenticationManager;

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PrivilegeService privilegeService;
    private final RolePrivilegeRepository rolePrivilegeRepository;
    private final UserPrivilegeRepository userPrivilegeRepository;
    private final UserRoleRepository userRoleRepository;
    private final UserBranchRepository userBranchRepository;

    private final SystemSettingService systemSettingService;
    private final CompanyService companyService;
    private final PatientRepository patientRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final ClaimRepository claimRepository;
    private final NoteRepository noteRepository;
    private final QuickSightAccess quickSightAccess;
    private final CalendarTemplateService calendarTemplateService;

    @PersistenceContext(name = "masterEntityManager", unitName = "master", type = PersistenceContextType.EXTENDED)
    @Qualifier("masterEntityManager")
    private EntityManager masterEntityManager;

    private Branch currentBranch;

    @Value("${aws.account.id:************}")
    private String awsAccountId;

    @Value("${aws.user.access.region.name:us-east-1}")
    private String userAccessRegionName;
    @Value("${aws.access.key:}")
    private static String AWS_ACCESS_KEY;
    @Value("${aws.secret.key:}")
    private static String AWS_SECRET_KEY;

    private final Map<String, String> userArn = new HashMap<>();
    private final Map<String, Boolean> attemptedQuickSightAccess = new HashMap<>();
    private final Map<String, String> quickSightUserAccessErrorMessage = new HashMap<>();

    @Value("${aws.quicksight.data.access.region:us-east-1}")
    private String awsQuickSightDataAccessRegion;

    private static final int CHECK_QUICKSIGHT_PERMISSIONS_RETRY_TOTAL = 3;

    @Autowired
    private PasswordHistoryService passwordHistoryService;

    @Autowired
    public UserService(UserRepository userRepository,
                       RoleRepository roleRepository,
                       PrivilegeService privilegeService,
                       RolePrivilegeRepository rolePrivilegeRepository,
                       UserPrivilegeRepository userPrivilegeRepository,
                       UserRoleRepository userRoleRepository,
                       UserBranchRepository userBranchRepository,
                       SystemSettingService systemSettingService,
                       CompanyService companyService,
                       PatientRepository patientRepository,
                       PrescriptionRepository prescriptionRepository,
                       ClaimRepository claimRepository,
                       NoteRepository noteRepository,
                       @Lazy AuthenticationManager authenticationManager,
                       QuickSightAccess quickSightAccess,
                       CalendarTemplateService calendarTemplateService) {
        super(userRepository);
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.privilegeService = privilegeService;
        this.rolePrivilegeRepository = rolePrivilegeRepository;
        this.userPrivilegeRepository = userPrivilegeRepository;
        this.userRoleRepository = userRoleRepository;
        this.userBranchRepository = userBranchRepository;
        this.systemSettingService = systemSettingService;
        this.companyService = companyService;
        this.patientRepository = patientRepository;
        this.prescriptionRepository = prescriptionRepository;
        this.claimRepository = claimRepository;
        this.noteRepository = noteRepository;
        this.authenticationManager = authenticationManager;
        this.quickSightAccess = quickSightAccess;
        this.calendarTemplateService = calendarTemplateService;
    }

    /**
     * Search users by keywords
     *
     * @param keyword
     * @param active
     * @param userGroup
     * @return List of users based on param values.<br>
     * If specification is null, return the whole table.
     */
    @SuppressWarnings(value = "unchecked")
    public List<User> search(String keyword, Boolean active, Boolean canHaveAppointments, Long userGroup, Long[] companyIds) {
        Role role = userGroup == null ? null : roleRepository.findById(userGroup).get();
        List<User> userList = new ArrayList<>();
        for (Long companyId : companyIds) {
            if (active != null && active) {
                userList.addAll(this.getActiveUsersForCompanyByCompanyId(companyId));
            } else {
                userList.addAll(this.getUsersForCompanyByCompanyId(companyId));
            }
        }

        if (role != null) {
            boolean hasRole = false;

            for (Iterator<User> iter = userList.listIterator(); iter.hasNext(); ) {
                User user = iter.next();
                List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());
                for (UserRole ur : userRoles) {
                    Role r = roleRepository.findById(ur.getRoleId()).get();
                    if (role.equals(r)) {
                        hasRole = true;
                    }
                }
                if (!hasRole) {
                    iter.remove();
                }
                hasRole = false;
            }
        }

        if (canHaveAppointments != null && canHaveAppointments) {
            boolean hasCanHaveAppointments = false;
            for (Iterator<User> iter = userList.listIterator(); iter.hasNext(); ) {
                User user = iter.next();
                if (user.getCanHaveAppointments() != null && user.getCanHaveAppointments()) {
                    hasCanHaveAppointments = true;
                }
                if (!hasCanHaveAppointments) {
                    iter.remove();
                }
                hasCanHaveAppointments = false;
            }
        }

        if (keyword != null && !"".equals(keyword.trim())) {
            boolean hasKeyword = false;
            // case insensitive...SCRUM-2993
            String lowerCaseKeyWord = keyword.toLowerCase();
            for (Iterator<User> iter = userList.listIterator(); iter.hasNext(); ) {
                User user = iter.next();
                if (user.getLastName().toLowerCase().contains(lowerCaseKeyWord)
                        || user.getFirstName().toLowerCase().contains(lowerCaseKeyWord)
                        || user.getUsername().toLowerCase().contains(lowerCaseKeyWord)) {
                    hasKeyword = true;
                }
                if (!hasKeyword) {
                    iter.remove();
                }
                hasKeyword = false;
            }
        }
        return userList;
    }

    @Transactional("masterTransactionManager")
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username);
        if (user == null)
            throw new UsernameNotFoundException("User: " + username + " not found");

        Collection<? extends GrantedAuthority> authorities = getAuthorities(user);
        return new org.springframework.security.core.userdetails.User(user.getUsername(),
                user.getPassword(), authorities);
    }

    private Collection<? extends GrantedAuthority> getAuthorities(User user) {
        return getGrantedAuthorities(getPrivileges(user));
    }

    public List<String> getPrivileges(User user) {
        Set<String> result = new HashSet<>();

        // Add superadmin privilege for the user with ID 1 or if the user is a superadmin
        if (user.getId() == 1 || user.getIsSuperAdmin()) {
            result.add("superadmin");
        }

        String tenant = TenantContext.getCurrentTenant();
        if (!"unknown".equals(tenant)) {
            // Fetch user privileges and role privileges in a single query for efficiency
            List<UserPrivilege> userPrivileges = userPrivilegeRepository.findByUserId(user.getId());
            List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());

            // Collect privilege IDs from both user privileges and role privileges
            Set<Long> privilegeIds = new HashSet<>();

            // Collect privilege IDs from direct user privileges
            for (UserPrivilege up : userPrivileges) {
                privilegeIds.add(up.getPrivilegeId());
            }

            // Collect privilege IDs from roles
            for (UserRole ur : userRoles) {
                List<RolePrivilege> rolePrivileges = rolePrivilegeRepository.findByRoleId(ur.getRoleId());
                for (RolePrivilege rp : rolePrivileges) {
                    privilegeIds.add(rp.getPrivilegeId());
                }
            }

            // Resolve privilege names and add to result
            List<String> privilegeNames = privilegeService.findByIdIn(new ArrayList<>(privilegeIds))
                    .stream()  // Convert the list to a stream
                    .map(Privilege::getName)  // Extract the name from each Privilege object
                    .toList();  // Collect the names into a list

            result.addAll(privilegeNames);
        }

        return new ArrayList<>(result);
    }


    private List<GrantedAuthority> getGrantedAuthorities(List<String> privileges) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        for (String privilege : privileges) {
            authorities.add(new SimpleGrantedAuthority(privilege));
        }
        return authorities;
    }

    public void savePrivileges(Long userId, List<Privilege> privileges) {
        User user = findOne(userId);
        this.updateUserPrivileges(privileges, user);
    }

    public void updateUserPrivileges(List<Privilege> incoming, User user) {
        List<UserPrivilege> existing = userPrivilegeRepository.findByUserId(user.getId());

        List<Long> existingIds = new ArrayList<>();
        List<Long> incomingIds = new ArrayList<>();
        List<Long> deleting = new ArrayList<>();
        List<Long> adding = new ArrayList<>();

        for (UserPrivilege e : existing)
            existingIds.add(e.getPrivilegeId());

        for (Privilege i : incoming)
            incomingIds.add(i.getId());

        //Delete existing if not in incoming list
        for (Long existingId : existingIds) {
            if (!incomingIds.contains(existingId)) {
                deleting.add(existingId);
            }
        }
        //Add new entries
        for (Long incomingId : incomingIds) {
            if (!existingIds.contains(incomingId)) {
                adding.add(incomingId);
            }
        }
        if (deleting.size() > 0) {
            for (Long id : deleting) {
                userPrivilegeRepository.deleteByUserIdAndPrivilegeId(user.getId(), id);
            }
        }
        if (adding.size() > 0) {
            for (Long id : adding) {
                UserPrivilege up = new UserPrivilege();
                up.setUserId(user.getId());
                up.setPrivilegeId(id);
                userPrivilegeRepository.save(up);
            }
        }
    }

    public void createUserTemplate(User user) {
        List<CalendarTemplate> ct = calendarTemplateService.findByUserId(user.getId());
        if (ct.isEmpty()) {
            CalendarTemplate calendarTemplate = new CalendarTemplate();
            calendarTemplate.setName("Default");
            calendarTemplate.setUserId(user.getId());
            calendarTemplateService.saveCalendarTemplate(calendarTemplate);
        }
    }

    public void updateUserRoles(UserDTO dto, User user) {
        List<UserRole> existing = userRoleRepository.findByUserId(user.getId());
        List<UserRole> incoming = dto.getUserRoles();

        List<Long> existingIds = new ArrayList<>();
        List<Long> incomingIds = new ArrayList<>();
        List<Long> deleting = new ArrayList<>();
        List<Long> adding = new ArrayList<>();

        for (UserRole e : existing)
            existingIds.add(e.getRoleId());

        for (UserRole i : incoming)
            incomingIds.add(i.getRoleId());

        //Delete existing if not in incoming list
        for (Long existingId : existingIds) {
            if (!incomingIds.contains(existingId)) {
                deleting.add(existingId);
            }
        }
        //Add new entries
        for (Long incomingId : incomingIds) {
            if (!existingIds.contains(incomingId)) {
                adding.add(incomingId);
            }
        }
        if (deleting.size() > 0) {
            for (Long id : deleting) {
                userRoleRepository.deleteByUserIdAndRoleId(user.getId(), id);
            }
        }
        if (adding.size() > 0) {
            for (Long id : adding) {
                UserRole ur = new UserRole();
                ur.setUserId(user.getId());
                ur.setRoleId(id);
                userRoleRepository.save(ur);
            }
        }
    }

    public void updateRolePrivileges(List<Privilege> incoming, Role role) {
        List<RolePrivilege> existing = rolePrivilegeRepository.findByRoleId(role.getId());

        List<Long> existingIds = new ArrayList<>();
        List<Long> incomingIds = new ArrayList<>();
        List<Long> deleting = new ArrayList<>();
        List<Long> adding = new ArrayList<>();

        for (RolePrivilege rp : existing)
            existingIds.add(rp.getPrivilegeId());

        for (Privilege p : incoming)
            incomingIds.add(p.getId());

        //Delete existing if not in incoming list
        for (Long existingId : existingIds) {
            if (!incomingIds.contains(existingId)) {
                deleting.add(existingId);
            }
        }
        //Add new entries
        for (Long incomingId : incomingIds) {
            if (!existingIds.contains(incomingId)) {
                adding.add(incomingId);
            }
        }
        if (deleting.size() > 0) {
            for (Long id : deleting) {
                rolePrivilegeRepository.deleteByRoleIdAndPrivilegeId(role.getId(), id);
            }
        }
        if (adding.size() > 0) {
            for (Long id : adding) {
                RolePrivilege rp = new RolePrivilege();
                rp.setPrivilegeId(id);
                rp.setRoleId(role.getId());
                rolePrivilegeRepository.save(rp);
            }
        }
    }

    public void updateUserBranches(UserDTO dto, User user) {
        List<UserBranch> existing = userBranchRepository.findByUserId(user.getId());
        List<UserBranch> incoming = dto.getUserBranches();

        List<Long> existingIds = new ArrayList<>();
        List<Long> incomingIds = new ArrayList<>();
        List<Long> deleting = new ArrayList<>();
        List<Long> adding = new ArrayList<>();

        for (UserBranch e : existing)
            existingIds.add(e.getBranchId());

        for (UserBranch i : incoming)
            incomingIds.add(i.getBranchId());

        //Delete existing if not in incoming list
        for (Long existingId : existingIds) {
            if (!incomingIds.contains(existingId)) {
                deleting.add(existingId);
            }
        }
        //Add new entries
        for (Long incomingId : incomingIds) {
            if (!existingIds.contains(incomingId)) {
                adding.add(incomingId);
            }
        }
        if (deleting.size() > 0) {
            for (Long id : deleting) {
                userBranchRepository.deleteByUserIdAndBranchId(user.getId(), id);
            }
        }
        if (adding.size() > 0) {
            for (Long id : adding) {
                UserBranch ub = new UserBranch();
                ub.setUserId(user.getId());
                ub.setBranchId(id);
                userBranchRepository.save(ub);
            }
        }
    }

    public User saveNotificationTypes(Long userId, List<NotificationType> notificationTypes) {
        User user = findOne(userId);
        user.setNotificationTypes(notificationTypes);
        user = save(user);
        return user;
    }

    public User saveWidgets(Long userId, List<Widget> widgets) {
        User user = findOne(userId);
        user.setWidgets(widgets);
        save(user);
        user = findOne(userId);
        return user;
    }

    public User getUserById(Long id) {
        User user = null;
        if (id != null) {
            user = findOne(id);
        }
        return user;
    }


    public List<User> findAllUsersByIdList(List<Long> ids) {
        List<User> users = new ArrayList<>();
        if (ids != null && !ids.isEmpty()) {
            users = userRepository.findAllById(ids);
        }
        return users;
    }

//    @Cacheable(value="users", key="#id")
//    public User findOne(Long id) {
//        if (id == null) {
//            return null;
//        }
//        Optional<User> optionalT = userRepository.findById(id);
//        if (optionalT.isPresent()) {
//            User result = optionalT.get();
//            loadForeignKeys(result);
//            return result;
//        } else {
//            return null;
//        }
//    }

    public User getCurrentUser() {
        User result = userRepository.findByUsername(SecurityUtils.getCurrentLogin());
        return result;
    }

    public User getByNylasAccountId(String nylasAccountId) {
        return userRepository.findByNylasAccountId(nylasAccountId);
    }

    public User getByNylasGrantId(String grantId) {
        return userRepository.findByNylasGrantId(grantId);
    }

    public List<User> findAllBySmsCompliantCellIsFalse(Long userId, String phoneNumber, String tenant) {
//        System.out.println("findAllBySmsCompliantCellIsFalseQuery =\n"+userRepository.findAllBySmsCompliantCellIsFalseQuery
//            .replaceAll(":phoneNumber", phoneNumber == null ? "''" : "'" + phoneNumber + "'")
//            .replaceAll(":userId", userId == null ? "null" : userId.toString())
//            .replaceAll(":tenant", tenant == null ? "''" : "'" + tenant + "'")
//        );
        List<User> results = userRepository.findAllBySmsCompliantCellIsFalse(userId, phoneNumber, tenant);
        return results;
    }

    public List<User> getActiveUsersByRoleAndCompanyNoSuperAdmin(Long roleId, Long companyId) {
        List<User> userList = this.getActiveUsersByRoleAndCompany(roleId, companyId);
        List<User> refinedList = noSuperAdmin(userList);
        return refinedList;
    }

    public List<User> getActivePractitionersAndTechnicians(Long companyId) {
        List<User> finalList = new ArrayList<>();
        getUsersForCompanyByCompanyId(companyId).forEach( user -> {
            List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());
            for(UserRole userRole : userRoles) {
                if(Boolean.TRUE.equals(user.getActive()) && (userRole.getRoleId() == 1 || userRole.getRoleId() == 3)) {
                    finalList.add(user);
                    break;
                }
            }
        });
        return finalList;
    }

    public List<User> noSuperAdmin(List<User> userList) {
        List<User> refinedList = new ArrayList<>();
        for (User user : userList) {
            if (user.getIsSuperAdmin() == null || (user.getIsSuperAdmin() != null && !user.getIsSuperAdmin())) {
                refinedList.add(user);
            }
        }
        return refinedList;
    }

    public List<User> getActiveUsersForCompanyByCompanyIdNoSuperAdmin(Long companyId) {
        List<User> userList = getActiveUsersForCompanyByCompanyId(companyId);
        List<User> refinedList = noSuperAdmin(userList);
        return refinedList;
    }

    public List<User> getActiveUsersForCompanyByCompanyIdNoSuperAdminSearchByName(Long companyId, String name) {
        List<User> userList = getActiveUsersForCompanyByCompanyId(companyId);
        List<User> noSuperAdmin = noSuperAdmin(userList);
        List<User> searchByName = searchByName(name, noSuperAdmin);
        return searchByName;
    }

    public List<User> searchByName(String name, List<User> noSuperAdmin) {
        String lowerCaseName = name.toLowerCase();
        List<User> searchByName = new ArrayList<>();
        for (User user : noSuperAdmin) {
            if (user.getFirstName().toLowerCase().contains(lowerCaseName) ||
                    user.getLastName().toLowerCase().contains(lowerCaseName) ||
                    user.getUsername().toLowerCase().contains(lowerCaseName)) {
                searchByName.add(user);
            }
        }
        return searchByName;
    }

    public List<User> getActiveUsersByRoleAndCompany(Long roleId, Long companyId) {
        List<User> userList = getActiveUsersForCompanyByCompanyId(companyId);
        List<User> userByRoleAndByCompany = new ArrayList<>();
        for (User user : userList) {
            if (user.getActive() != null && user.getActive()) {
                List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());
                for (UserRole ur : userRoles) {
                    if (roleId.equals(ur.getRoleId())) {
                        userByRoleAndByCompany.add(user);
                    }
                }
            }
        }
        userByRoleAndByCompany.sort(Comparator.comparing(user -> user.getLastName()));
        return userByRoleAndByCompany;
    }

    public List<User> getActiveUsersByRoleNameAndCompany(String roleName, Long companyId) {
        List<User> userList = getActiveUsersForCompanyByCompanyId(companyId);
        List<User> userByRoleAndByCompany = new ArrayList<>();
        for (User user : userList) {
            if (user.getActive() != null && user.getActive()) {
                List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());
                for (UserRole ur : userRoles) {
                    Role role = roleRepository.findById(ur.getRoleId()).get();
                    if (role.getName().equals(roleName)) {
                        userByRoleAndByCompany.add(user);
                    }
                }
            }
        }
        userByRoleAndByCompany.sort(Comparator.comparing(user -> user.getLastName()));
        return userByRoleAndByCompany;
    }

    public List<User> getActiveCareExtendersAndPractitionersByCompany(Long companyId) {
        List<User> result = new ArrayList<>();
        result.addAll(getActiveUsersByRoleAndCompany(1L, companyId));

        List<User> careExtenders = getActiveUsersByRoleAndCompany(5L, companyId);
        for (User ce : careExtenders) {
            if (!result.contains(ce)) {
                result.add(ce);
            }
        }

        return result;
    }

    public List<User> getActivePractitionersByCompany(Long companyId) {
        List<User> result = new ArrayList<>();
        result.addAll(getActiveUsersByRoleAndCompany(1L, companyId));
        return result;
    }

    public List<User> getActiveCareExtendersByCompany(Long companyId) {
        List<User> result = new ArrayList<>();
        result.addAll(getActiveUsersByRoleAndCompany(5L, companyId));
        return result;
    }

    public List<User> getActiveUsersForCompanyByCompanyIdCanHaveAppointments(Long companyId) {
        List<User> userList = getActiveUsersForCompanyByCompanyId(companyId);

        List<User> finalUserList = new ArrayList<>();
        for (User u : userList) {
            if (u.getCanHaveAppointments() != null && u.getCanHaveAppointments()) {
                finalUserList.add(u);
            }
        }
        return finalUserList;
    }

    public List<User> getActiveUsersForCompanyByCompanyId(Long companyId) {
        List<User> userList = getUsersForCompanyByCompanyId(companyId);
        userList.removeIf(user -> !user.getActive());
        return userList;
    }

    public List<User> getUsersForCompanyByCompanyId(Long companyId) {
        List<User> userList = new ArrayList<>();
        Company company = companyService.findOne(companyId);
        if (company != null) {
            Set<User> userSet = new HashSet<>();
            userSet.addAll(company.getUsers());
            userSet.addAll(this.getUsersByCompanyId(companyId));
            userList.addAll(userSet);
            userList.sort(Comparator.comparing(user -> user.getFirstName()));
            userList.sort(Comparator.comparing(user -> user.getLastName()));
        }
        return userList;
    }

    public void addCurrentUser(List<User> results2, User current, User u) {
        Collection<Company> companies = u.getCompanies();
        for (Company company : companies) {
            if (current.getCompanyId().equals(company.getId())) {
                results2.add(u);
            }
        }
    }

    public List<User> getUsersByCompanyId(Long companyId) {
        List<User> result = userRepository.findByCompanyId(companyId);
        return result;
    }

    public User getUserByCompanyIdAndUserEmail(Long companyId, String userEmail) {
        User result = userRepository.findByCompanyIdAndEmail(companyId, userEmail);
        return result;
    }

    public List<User> getUserCalendarDefaultUsers(Long userId) {
        User user = getUserById(userId);
        List<User> users = new ArrayList<>();
        if (user.getCalendarDefaultUsers() != null) {
            List<Long> calendarUserIds = Stream.of(user.getCalendarDefaultUsers().split(",")).map(Long::parseLong).collect(Collectors.toList());
            for (Long calendarUserId : calendarUserIds) {
                users.add(getUserById(calendarUserId));
            }
        }
        return users;
    }

    public User findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    public List<User> findByIdIn(List<Long> ids) {
        return userRepository.findByIdInAndActiveIsTrue(ids);
    }

    // UNSAFE!
    public Branch getCurrentBranchUnsafeDoNotUse() {
        return currentBranch;
    }// UNSAFE!
    // UNSAFE!

    public void setCurrentBranch(Branch currentBranch) {
        this.currentBranch = currentBranch;
    }

    @ValidatePasswordHistory
    public void changePassword(String oldPass, String newPass) throws Exception {
        try {
            // Get current user
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                throw new Exception("No current user found");
            }

            // First, authenticate with old password to verify it's correct
            try {
                authenticationManager.authenticate(
                        new UsernamePasswordAuthenticationToken(
                                currentUser.getUsername(),
                                oldPass
                        )
                );
            } catch (AuthenticationException ex) {
                log.error(ErrorMessage.getFullInfo(ex));
                throw new Exception("Current password is incorrect");
            }

            // Password validation is handled by @ValidatePasswordHistory annotation
            // No need to call validatePasswordAgainstHistory() here as it would be duplicate

            // Update password and save
            BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
            currentUser.setPassword(encoder.encode(newPass));
            currentUser.setLastPasswordChangeDate(new java.sql.Timestamp(System.currentTimeMillis()));
            save(currentUser);
        } catch (Exception ex) {
            log.error(ErrorMessage.getFullInfo(ex));
            throw ex;
        }
    }

    public Company getCurrentCompany() {
        return companyService.findByKey(TenantContext.getCurrentTenant());
    }

    public AmazonQuickSight getClient(String regionName) {

        final AWSCredentialsProvider credsProvider = new AWSCredentialsProvider() {
            @Override
            public AWSCredentials getCredentials() {
                // provide actual IAM access key and secret key here
                return new BasicAWSCredentials(AWS_ACCESS_KEY, AWS_SECRET_KEY);
            }

            @Override
            public void refresh() {
            }
        };

        return AmazonQuickSightClientBuilder
                .standard()
                .withRegion(regionName)
                .build();
    }

    public String getUserArn(String email) {
        if (!userArn.containsKey(email)) {
            userArn.put(email, "");
            attemptedQuickSightAccess.put(email, false);
            quickSightUserAccessErrorMessage.put(email, "");
        }
        if (userArn.get(email).length() == 0) {
            if (email != null && email.contains("@") && !attemptedQuickSightAccess.get(email)) {
                DescribeUserResult describeUserResult = null;
                try {
                    attemptedQuickSightAccess.put(email, true);
                    DescribeUserRequest describeUserRequest = new DescribeUserRequest().withUserName(email).withAwsAccountId(awsAccountId).withNamespace("default").withRequestCredentialsProvider(new DefaultAWSCredentialsProviderChain()) ;
                    AmazonQuickSight amazonQuickSight = quickSightAccess.getClient(userAccessRegionName);
                    describeUserResult = amazonQuickSight.describeUser(describeUserRequest);
                } catch (Exception e) {
                    //throws an exception if they don't have access.
                    quickSightUserAccessErrorMessage.put(email, ErrorMessage.getFullInfo(e));
                    //log.error(quickSightUserAccessErrorMessage.get(email));
                }
                if (describeUserResult != null) {
                    quickSightUserAccessErrorMessage.put(email, "Access Successful");
                    userArn.put(email, describeUserResult.getUser().getArn());
                }
            }
        }
        return userArn.get(email);
    }

    public Boolean hasQuickSightAccess(String email) {
        return getUserArn(email).length() > 0;
    }

    public Boolean getAttemptedQuickSightAccess(String email) {
        return attemptedQuickSightAccess.get(email);
    }

    public void setAttemptedQuickSightAccess(String email, Boolean attemptedQuickSightAccess) {
        this.attemptedQuickSightAccess.put(email, attemptedQuickSightAccess);
    }

    public String getQuickSightUserAccessErrorMessage(String email) {
        return quickSightUserAccessErrorMessage.get(email);
    }

    public void setUserArn(String email, String arn) {
        this.userArn.put(email, arn);
    }

    public List<ClericalProductivityReportDTO> getClericalProductivityByUserIdBetween(Long branchId, ZonedDateTime startDate, ZonedDateTime endDateEOD, List<Long> userIds) {
        List<User> users = getActiveUsersForCompanyByCompanyId(getCurrentCompany().getId());
        List<ClericalProductivityReportDTO> dtos = new ArrayList<>();
        String[] noteTypes = new String[]{"general", "clinical", "billing_ar", "billing", "complaint", "patient_summary", "claim_comments"};
        HashMap<String, Integer> noteCounts = new HashMap<>();
        List<User> matchingUsers = null;
        if (userIds != null && !userIds.isEmpty())
            matchingUsers = users.stream().filter(u -> userIds.contains(u.getId())).collect(Collectors.toList());
        if (matchingUsers != null && !matchingUsers.isEmpty())
            users = matchingUsers;
        for (User user : users) {
            ClericalProductivityReportDTO dto = new ClericalProductivityReportDTO();
            DateTimeFormatter dateTimeFormatter = new DateTimeFormatterBuilder()
                    .append(DateTimeFormatter.ISO_LOCAL_DATE)
                    .toFormatter();
            // Needs to be next day to simulate EOD logic. Claims are using just a date.
            // Time of EOD is dropped in formatting, so we add a day
            String endEODDateString = endDateEOD.plus(1, ChronoUnit.DAYS).format(dateTimeFormatter);
            String dateStart = startDate.format(dateTimeFormatter);

            Timestamp dateEndPlusOneDay = Timestamp.valueOf(endDateEOD.toLocalDateTime());
            Timestamp startDateTimeStamp = Timestamp.valueOf(startDate.toLocalDateTime());
            Integer patientCount = patientRepository.getPatientCountByBranchAndCreatedByUserBetweenDate(branchId, user.getId(), startDateTimeStamp, dateEndPlusOneDay);
            Integer prescriptionCount = prescriptionRepository.getPrescriptionCountByBranchAndCreatedByUserBetweenDate(branchId, user.getId(), startDateTimeStamp, dateEndPlusOneDay);
            Integer claimCount = claimRepository.getClaimCountByBranchAndCreatedByUserBetweenDate(branchId, user.getId(), dateStart, endEODDateString);

            for (String noteType : noteTypes) {
                Integer notesCreatedCount = noteRepository.getNoteCountByBranchAndCreatedByUserBetweenDateAndNotetype(branchId, user.getId(), startDateTimeStamp, dateEndPlusOneDay, noteType);
                noteCounts.put(noteType, notesCreatedCount);
            }

            dto.setUser(user);
            dto.setPatientCreatedCount(patientCount);
            dto.setPrescriptionCreatedCount(prescriptionCount);
            dto.setClaimCreatedCount(claimCount);
            dto.setGeneralNotesCreated(noteCounts.get("general"));
            dto.setClinicalNotesCreated(noteCounts.get("clinical"));
            dto.setArNotesCreated(noteCounts.get("billing_ar"));
            dto.setBillingNotesCreated(noteCounts.get("billing"));
            dto.setComplaintNotesCreated(noteCounts.get("complaint"));
            dto.setRxSummaryNotesCreated(noteCounts.get("patient_summary"));
            dto.setClaimCommentsNotesCreated(noteCounts.get("claim_comments"));
            dtos.add(dto);
        }
        return (dtos);
    }

    public List<String> populateQuickSightDashboardEmbedUrlList() {
        List<String> embedUrlList = new ArrayList<>();
        User user = getCurrentUser();
        if (hasQuickSightAccess(user.getEmail())) {
            trackWhenUserGainsQuickSightAccessForAccounting(user);
            String userArn = getUserArn(user.getEmail());
            AmazonQuickSight client = quickSightAccess.getClient(awsQuickSightDataAccessRegion);
            ListDashboardsResult listDashboardsResult = client.listDashboards(new ListDashboardsRequest().withAwsAccountId(awsAccountId).withRequestCredentialsProvider(new DefaultAWSCredentialsProviderChain()));
            for (DashboardSummary dashboardSummary : listDashboardsResult.getDashboardSummaryList()) {
                for (int index = 0; index < CHECK_QUICKSIGHT_PERMISSIONS_RETRY_TOTAL + 1; index++) {
                    String dashboardId = dashboardSummary.getDashboardId();
                    try {
                        DescribeDashboardPermissionsResult describeDashboardPermissionsResult = client.describeDashboardPermissions(
                                new DescribeDashboardPermissionsRequest().withDashboardId(dashboardId).withAwsAccountId(awsAccountId).withRequestCredentialsProvider(new DefaultAWSCredentialsProviderChain()));
                        List<ResourcePermission> permissionList = describeDashboardPermissionsResult.getPermissions();
                        for (ResourcePermission resourcePermission : permissionList) {
                            if (userArn.equals(resourcePermission.getPrincipal())) {
                                String embedUrl = client.getDashboardEmbedUrl(new GetDashboardEmbedUrlRequest()
                                        .withDashboardId(dashboardId)
                                        .withAwsAccountId(awsAccountId)
                                        .withUserArn(userArn)
                                        .withIdentityType(IdentityType.QUICKSIGHT.toString())
                                        .withRequestCredentialsProvider(new DefaultAWSCredentialsProviderChain())
//                                .withResetDisabled(true)
//                                .withSessionLifetimeInMinutes(100l)
//                                .withUndoRedoDisabled(false)
                                ).getEmbedUrl();

                                embedUrlList.add(dashboardSummary.getName() + embedUrl);
                            }
                        }
                        break; // completed successfully, no need to retry
                    } catch (ThrottlingException e) {
                        log.warn("Retry check QuickSight permissions for " + dashboardId + " of count " + (index + 1));
                        try {
                            Thread.sleep(10_000L);
                        } catch (InterruptedException e2) {
                            log.warn("Thread sleep interrupted unexpectedly");
                        }
                    }
                }
            }
        }
        return embedUrlList;
    }

    public String exportUsers(Long[] companyIds) {
        String csv = "ID, Active, First Name, Last Name, Username, Phone Number, Email, Manager 1, Manager 2, Manager 3, User Roles,\n";
        List<User> users = search("", null, null, null, companyIds);
        for (User user : users) {
            csv = csv.concat(user.getId() != null ? user.getId().toString() : "");
            csv = csv.concat(",").concat(user.getActive() != null ? user.getActive().toString() : "");
            csv = csv.concat(",").concat(user.getFirstName() != null ? user.getFirstName() : "");
            csv = csv.concat(",").concat(user.getLastName() != null ? user.getLastName() : "");
            csv = csv.concat(",").concat(user.getUsername() != null ? user.getUsername() : "");
            csv = csv.concat(",").concat(user.getPhoneNumber() != null ? user.getPhoneNumber() : "");
            csv = csv.concat(",").concat(user.getEmail() != null ? user.getEmail() : "");
            csv = csv.concat(",").concat(user.getManager() != null ? getUserById(user.getManagerId()).getLastName() : "");
            csv = csv.concat(",").concat(user.getManagerTwo() != null ? getUserById(user.getManagerTwoId()).getLastName() : "");
            csv = csv.concat(",").concat(user.getManagerThree() != null ? getUserById(user.getManagerThreeId()).getLastName() : "");

            // Loop User Roles
            csv = csv.concat(",");
            List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());
            for (UserRole ur : userRoles) {
                Role role = roleRepository.findById(ur.getRoleId()).get();
                csv = csv.concat(" - ").concat(role.getName());
            }

            csv = csv.concat("\n");
        }
        return csv;
    }

    public void trackWhenUserGainsQuickSightAccessForAccounting(User user) {
        if (user.getIsQuicksightUser() == null || !user.getIsQuicksightUser()) {
            user.setIsQuicksightUser(true);
            save(user);
        }
    }

    public void setUserAccessRegionName(String userAccessRegionName) {
        this.userAccessRegionName = userAccessRegionName;
    }

//    public void setAwsAccountId(String awsAccountId) {
//        this.awsAccountId = awsAccountId;
//    }

    public void setAwsQuickSightDataAccessRegion(String awsQuickSightDataAccessRegion) {
        this.awsQuickSightDataAccessRegion = awsQuickSightDataAccessRegion;
    }

    public List<Privilege> getAllPrivilegesForUserById(Long userId) {

        List<Long> privilegeIds = new ArrayList<>();

        List<UserPrivilege> userPrivileges = userPrivilegeRepository.findByUserId(userId);
        for (UserPrivilege up : userPrivileges) {
            if (!privilegeIds.contains(up.getPrivilegeId())) {
                privilegeIds.add(up.getPrivilegeId());
            }
        }

        List<UserRole> userRoles = userRoleRepository.findByUserId(userId);
        for (UserRole ur : userRoles) {
            List<RolePrivilege> rolePrivileges = rolePrivilegeRepository.findByRoleId(ur.getRoleId());
            for (RolePrivilege rp : rolePrivileges) {
                if (!privilegeIds.contains(rp.getPrivilegeId())) {
                    privilegeIds.add(rp.getPrivilegeId());
                }
            }
        }

        List<Privilege> results = privilegeService.findByIdIn(privilegeIds);
        return results;
    }

    public List<User> getActiveEmployeesByManagerId(Long userId) {
        List<User> results = userRepository.getActiveEmployeesByManagerId(userId);
        return results;
    }

    public List<QuicksightAssetUserPermissions> getUserAssetPermissions() {
        List<QuicksightAssetUserPermissions> results = new ArrayList<>();

        EntityManager em = masterEntityManager.getEntityManagerFactory().createEntityManager();
        Session session = em.unwrap(Session.class);
        session.doWork(connection -> {
            connection.setReadOnly(false);
            CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}");
            boolean hasResults = stmt.execute();
            if (hasResults) {
                ResultSet rs = stmt.getResultSet();
                while (rs.next()) {
                    QuicksightAssetUserPermissions assetUserPermissions = new QuicksightAssetUserPermissions(rs.getString("UserName"), rs.getString("tenant"));
                    results.add(assetUserPermissions);
                }
                rs.close();
            }
            connection.close();
        });
        session.close();
        em.close();
        return results;
    }

    public List<QuicksightTenantUserPermission> getUserTenantPermissions() {
        List<QuicksightTenantUserPermission> results = new ArrayList<>();

        EntityManager em = masterEntityManager.getEntityManagerFactory().createEntityManager();
        Session session = em.unwrap(Session.class);
        session.doWork(connection -> {
            connection.setReadOnly(false);
            CallableStatement stmt = connection.prepareCall("{call userTenantPermissions()}");
            boolean hasResults = stmt.execute();
            if (hasResults) {
                ResultSet rs = stmt.getResultSet();
                while (rs.next()) {
                    QuicksightTenantUserPermission tenantUserPermissions = new QuicksightTenantUserPermission(
                            rs.getString("tenant"),
                            rs.getString("company_name"),
                            rs.getBoolean("company_active"),
                            rs.getString("last_name"),
                            rs.getString("first_name"),
                            rs.getString("username"),
                            rs.getBoolean("user_active"),
                            rs.getBoolean("is_multi_user"),
                            rs.getBoolean("direct_scan_access"),
                            rs.getString("user_branches")
                    );
                    results.add(tenantUserPermissions);
                }
                rs.close();
            }
            connection.close();
        });
        session.close();
        em.close();
        return results;
    }


    @Override
    public void loadForeignKeys(User o) {
//        if (o != null) {
//            o.setCompanies(companyService.findCompanyByUsers(Arrays.asList(o)));
//        }
    }

    /**
     * Validates a new password against the user's password history
     * @param userId The ID of the user
     * @param newPassword The new password to validate
     * @throws Exception if the password violates history rules
     */
    public void validatePasswordAgainstHistory(Long userId, String newPassword) throws Exception {
        if (userId == null) {
            return; // Skip validation for new users
        }

        // Get the existing user
        User user = findOne(userId);
        if (user == null) {
            throw new Exception("User not found");
        }

        // Check if the new password matches the current password
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        if (encoder.matches(newPassword, user.getPassword())) {
            throw new Exception("New password cannot be the same as your current password");
        }

        // Check password history
        if (passwordHistoryService.isPasswordInHistory(userId, newPassword)) {
            int historyDepth = passwordHistoryService.getHistoryDepth();
            int reuseDays = passwordHistoryService.getPasswordReuseDays();
            throw new Exception(
                "Password cannot be reused. Please choose a password that has not been used in your last "
                + historyDepth + " password changes or within the last " + reuseDays + " days."
            );
        }

        // Validate password requirements
        var requireMixedCaseSetting = systemSettingService.findBySectionAndField("password", "require_mixed_case");
        var requireSpecialCharacterSetting = systemSettingService.findBySectionAndField("password", "require_special_character");
        var minPasswordLengthSetting = systemSettingService.findBySectionAndField("password", "minimum_length");

        boolean requireMixedCase = requireMixedCaseSetting != null && "Y".equalsIgnoreCase(requireMixedCaseSetting.getValue());
        boolean requireSpecialCharacter = requireSpecialCharacterSetting != null && "Y".equalsIgnoreCase(requireSpecialCharacterSetting.getValue());
        int minPasswordLength = minPasswordLengthSetting != null && minPasswordLengthSetting.getValue() != null ?
                Integer.parseInt(minPasswordLengthSetting.getValue()) : 6;

        if (newPassword.length() < minPasswordLength) {
            throw new Exception("Password must be at least " + minPasswordLength + " characters.");
        }

        // Check for at least one special character (non-alphanumeric)
        Pattern regex = Pattern.compile("[^a-zA-Z0-9]");
        Matcher matcher = regex.matcher(newPassword);
        if (requireSpecialCharacter && !matcher.find()) {
            throw new Exception("Password must contain at least one special character (non-alphanumeric)");
        }

        // Check for mixed case
        regex = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z]).*$");
        matcher = regex.matcher(newPassword);
        if (requireMixedCase && !matcher.find()) {
            throw new Exception("Password must contain both uppercase and lowercase characters");
        }
    }

    /**
     * Save a user with password change, bypassing password history validation for admin changes
     * @param user The user to save
     * @param skipPasswordValidation Whether to skip password validation (true for admin changes)
     * @return The saved user
     */
    public User saveWithPassword(User user, boolean skipPasswordValidation) {
        if (user.getPassword() != null && !skipPasswordValidation) {
            try {
                validatePasswordAgainstHistory(user.getId(), user.getPassword());
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        }
        return save(user);
    }

    @Transactional("masterTransactionManager")
    public User saveDTO(User user) throws Exception {
        try {
            log.info("Starting saveDTO for user: {}", user.getUsername());

            if (user.getId() == null) {
                log.info("New user creation - validating username");
                // New user - validate username doesn't exist
                if (userRepository.findByUsername(user.getUsername()) != null) {
                    log.warn("Username already exists: {}", user.getUsername());
                    throw new Exception("Username already exists");
                }
            }

            // If password is provided, encode it without validation (admin change)
            if (user.getPassword() != null && !user.getPassword().isEmpty()) {
                log.info("Password provided - encoding password for user: {}", user.getUsername());
                // Skip password history validation for admin changes
                BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
                user.setPassword(encoder.encode(user.getPassword()));
                user.setLastPasswordChangeDate(new java.sql.Timestamp(System.currentTimeMillis()));
                log.info("Password encoded and last change date updated");
            } else if (user.getId() != null) {
                log.info("Existing user without password change - preserving current password");
                // Existing user - keep current password if not provided
                User existingUser = findOne(user.getId());
                if (existingUser != null) {
                    user.setPassword(existingUser.getPassword());
                    user.setLastPasswordChangeDate(existingUser.getLastPasswordChangeDate());
                    log.info("Existing password preserved for user: {}", user.getUsername());
                }
            }

            log.info("Attempting to save user: {}", user.getUsername());
            User savedUser = save(user);
            if (savedUser == null) {
                log.error("Failed to save user - save operation returned null");
                throw new Exception("Failed to save user");
            }
            log.info("User saved successfully: {}", savedUser.getUsername());
            return savedUser;
        } catch (Exception ex) {
            log.error("Error saving user: {} - Exception: {}", user.getUsername(), ErrorMessage.getFullInfo(ex));
            throw new Exception("Failed to save user: " + ex.getMessage());
        }
    }
}
