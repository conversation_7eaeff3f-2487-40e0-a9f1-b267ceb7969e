package com.nymbl.config.security;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;


import com.nymbl.tenant.service.SystemSettingService;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 * Default implementation of PasswordHistoryService that uses Hibernate Envers
 * to check password history from audit tables.
 */
@Service
public class DefaultPasswordHistoryService implements PasswordHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultPasswordHistoryService.class);

    @PersistenceContext(name = "masterEntityManager", unitName = "master")
    @Qualifier("masterEntityManager")
    private EntityManager entityManager;

    private final PasswordEncoder passwordEncoder;
    private final SystemSettingService systemSettingService;

    @Value("${password.history.depth:6}")
    private int historyDepth;

    @Value("${password.history.enabled:true}")
    private boolean enabled;

    @Value("${password.reuse.days:365}")
    private int passwordReuseDays;

    @Autowired
    public DefaultPasswordHistoryService(PasswordEncoder passwordEncoder,
                                         SystemSettingService systemSettingService) {
        this.passwordEncoder = passwordEncoder;
        this.systemSettingService = systemSettingService;
    }

    // Constructor for backward compatibility
    public DefaultPasswordHistoryService(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
        this.systemSettingService = null;
    }

    @Override
    public boolean isPasswordInHistory(Long userId, String newPass) {
        if (!enabled) {
            return false;
        }

        int depth = getHistoryDepth();
        int reuseDays = getPasswordReuseDays();

        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(reuseDays);
        Timestamp cutoffTimestamp = Timestamp.valueOf(cutoffDate);

        // Use a single optimized query that combines both time and depth constraints
        // Limit to reasonable number to avoid checking hundreds of passwords
        String sql = """
            SELECT ua.password
            FROM user_audit ua
            JOIN audit_revision ar ON ua.revision_id = ar.revision_id
            WHERE ua.id = :userId
            AND ua.password IS NOT NULL
            AND (ar.rev_timestamp >= :cutoffTimestamp OR ua.revision_id IN (
                SELECT ua2.revision_id FROM user_audit ua2
                JOIN audit_revision ar2 ON ua2.revision_id = ar2.revision_id
                WHERE ua2.id = :userId AND ua2.password IS NOT NULL
                ORDER BY ar2.rev_timestamp DESC
                LIMIT :depth
            ))
            ORDER BY ar.rev_timestamp DESC
            LIMIT 50
            """;

        try {
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("userId", userId);
            query.setParameter("cutoffTimestamp", cutoffTimestamp);
            query.setParameter("depth", depth);

            @SuppressWarnings("unchecked")
            List<String> passwords = (List<String>) query.getResultList();

            // Check passwords one by one and return immediately on first match
            for (String oldPass : passwords) {
                if (passwordEncoder.matches(newPass, oldPass)) {
                    logger.debug("Password found in history");
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            logger.warn("Error checking password history: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getHistoryDepth() {
        try {
            var setting = systemSettingService.findBySectionAndField("password", "password_history_depth");
            if (setting != null) {
                // Try to access the value using reflection as a fallback for IDE issues
                String value;
                try {
                    // First try the standard getter
                    value = setting.getValue();
                } catch (Exception ex) {
                    try {
                        // If that fails, try direct field access via reflection
                        java.lang.reflect.Field valueField = setting.getClass().getDeclaredField("value");
                        valueField.setAccessible(true);
                        value = (String) valueField.get(setting);
                    } catch (Exception e) {
                        // If all else fails, use the default value
                        logger.warn("Failed to access value field via reflection: {}", e.getMessage());
                        return historyDepth;
                    }
                }
                int parsedValue = Integer.parseInt(value);
                return parsedValue > 0 ? parsedValue : historyDepth;
            }
            return historyDepth;
        } catch (Exception e) {
            logger.warn("Failed to get password history depth from settings, using default: {}", historyDepth, e);
            return historyDepth;
        }
    }

    @Override
    public int getPasswordReuseDays() {
        try {
            var setting = systemSettingService.findBySectionAndField("password", "password_reuse_days");
            if (setting != null) {
                // Try to access the value using reflection as a fallback for IDE issues
                String value;
                try {
                    // First try the standard getter
                    value = setting.getValue();
                } catch (Exception ex) {
                    try {
                        // If that fails, try direct field access via reflection
                        java.lang.reflect.Field valueField = setting.getClass().getDeclaredField("value");
                        valueField.setAccessible(true);
                        value = (String) valueField.get(setting);
                    } catch (Exception e) {
                        // If all else fails, use the default value
                        logger.warn("Failed to access value field via reflection: {}", e.getMessage());
                        return passwordReuseDays;
                    }
                }
                int parsedValue = Integer.parseInt(value);
                return parsedValue > 0 ? parsedValue : passwordReuseDays;
            }
            return passwordReuseDays;
        } catch (Exception e) {
            logger.warn("Failed to get password reuse days from settings, using default: {}", passwordReuseDays, e);
            return passwordReuseDays;
        }
    }
}
