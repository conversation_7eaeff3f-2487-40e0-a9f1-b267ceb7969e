<ng-include src="'views/tmpl/maintenance/_view_denied.html'"></ng-include>
<div class="page page-forms-common" ng-if="hasPermission(page.view + '_view')">
    <!-- page header -->
    <div class="pageheader">
        <h2>{{page.title}} <span ng-bind-html="page.subtitle"></span></h2>
        <div id="header-alert-container" class="alert" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a href="javascript:;">{{page.title}}</a>
                </li>
                <li>
                    <a ui-sref="app.maintenance.branch">{{page.subtitle}}</a>
                </li>
            </ul>
        </div>
        <div class="loading-indicator"></div>
    </div>
    <div id="body-container">
        <div class="panel panel-greensea mb-10">
            <div class="panel-body pt-10 pb-10">
                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group m-0">
                            <label>Search by keywords</label>
                            <input ng-model="filter.searchTerm" type="text" id="search-term"
                                   class="form-control input-sm" ng-change="search()"
                                   ng-model-options='{ debounce: 500 }' ng-disabled="editing">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group m-0">
                            <label>Show Active or Inactive Branches</label>
                            <div class="form-group">
                                <label class="checkbox-inline checkbox-custom">
                                    <input ng-model="filter.active" type="radio" value="1" ng-change="search()"
                                           ng-disabled="editing"><i></i> Active
                                </label>
                                <label class="checkbox-inline checkbox-custom">
                                    <input ng-model="filter.active" type="radio" value="0" ng-change="search()"
                                           ng-disabled="editing"><i></i> Inactive
                                </label>
                                <label class="checkbox-inline checkbox-custom">
                                    <input ng-model="filter.active" type="radio" value="" ng-change="search()"
                                           ng-disabled="editing"><i></i> Active & Inactive
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-offset-2 col-sm-2">
                        <div class="form-group m-0">
                            <label>Matches displayed</label>
                            <input ng-model="branches.length" type="text" class="form-control input-sm" disabled>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row pl-15">
            <div class="col-sm-3 custom-table" ng-style="{height: '609px'}">
                <div class="col-sm-12 header-fixed">
                    <div class="col-sm-12">Branches</div>
                </div>
                <div class="col-sm-12 custom-table-body" ng-style="{height: '575px'}">
                    <div ng-class="{active : activeRecord.id === branch.id}" class="col-sm-12"
                         ng-repeat="branch in branches" ng-click="setActive(branch)">
                        <div class="col-sm-12 text-nowrap">{{branch.$name}}</div>
                    </div>
                </div>
            </div>
            <div class="col-sm-9 mb-20">
                <form name="branchForm" ng-submit="save(branchForm)" class="col-sm-12 form-primary" novalidate>
                    <div ng-if="errorMessage" class="alert alert-danger">
                        {{ errorMessage }}
                    </div>
                    <div class="form-group text-right">
                        <audit-button entity="branch" ng-model="activeRecord.id" showlabel="{{true}}"></audit-button>
                        <ng-hide ng-if="!editing" ng-cloak>
                            <button type="button"
                                    id="delete-record"
                                    class="btn btn-sm btn-rounded btn-danger"
                                    ng-disabled="activeRecord === undefined"
                                    ng-if="hasPermission(page.view + '_delete')"
                                    ng-click="delete()">
                                <i class="fa fa-trash"></i>
                                Delete
                            </button>
                            <button type="button"
                                    id="edit-form"
                                    class="btn btn-sm btn-rounded btn-info"
                                    ng-disabled="activeRecord === undefined"
                                    ng-if="hasPermission(page.view + '_edit')"
                                    ng-click="edit()">
                                <i class="fa fa-edit"></i>
                                Edit
                            </button>
                            <button type="button"
                                    id="new-form"
                                    class="btn btn-sm btn-rounded btn-primary"
                                    ng-if="authService.isSuperAdmin()"
                                    ng-click="newRecord()">
                                <i class="fa fa-plus"></i>
                                New
                            </button>
                        </ng-hide>
                        <ng-hide
                                ng-if="editing && (hasPermission(page.view + '_add') || hasPermission(page.view + '_edit'))"
                                ng-cloak>
                            <button type="submit"
                                    id="save-form"
                                    class="btn btn-sm btn-rounded btn-success"
                                    data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Saving...">
                                <i class="fa fa-save"></i>
                                Save
                            </button>
                            <button type="button"
                                    id="cancel-form"
                                    class="btn btn-sm btn-rounded btn-default"
                                    ng-click="cancel()">
                                <i class="fa fa-times"></i>
                                Cancel
                            </button>
                        </ng-hide>
                    </div>
                    <div id="alert-container" class="alert" style="display: none;"></div>
                    <div class="row">
                        <div class="col-sm-4 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.name.$invalid, 'has-success' : submitted && branchForm.name.$valid}">
                            <label for="name">Branch Name</label>
                            <input type="text" name="name" id="name" class="form-control input-sm" required
                                   ng-disabled="!editing" ng-model="activeRecord.name" ng-cloak>
                            <div ng-messages="branchForm.name.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Branch name is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="is_active" value="1" ng-disabled="!editing"
                                       ng-model="activeRecord.active" ng-cloak><i></i> Active?
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4 form-group">
                            <label for="name">Parent Branch</label>
                            <select name="parentId"
                                    id="parentId"
                                    class="form-control input-sm chosen-select" ng-disabled="!editing"
                                    chosen
                                    ng-model="activeRecord.parentId" ng-cloak
                                    ng-options="b.id as b.name for b in activeBranches">
                                <option value=""></option>
                                <!--                                <option ng-repeat="b in activeBranches disable when b.id === activeRecord.parentId" value="{{b.id}}">{{b.name}}</option>-->
                            </select>
                        </div>
                        <!--<div class="col-sm-3 form-group" ng-class="{ 'has-error' : submitted && branchForm.code.$invalid, 'has-success' : submitted && branchForm.code.$valid}">-->
                        <!--<label for="code">Code</label>-->
                        <!--<input type="text" name="code" id="code" class="form-control input-sm"-->
                        <!--ng-pattern="/^\S{3}$/"-->
                        <!--ng-disabled="!editing" ng-model="activeRecord.code">-->
                        <!--<div ng-messages="branchForm.code.$error" role="alert" ng-show="submitted">-->
                        <!--<div class="help-block" ng-message="pattern">Invalid format. Connot be longer than 3 characters.</div>-->
                        <!--</div>-->
                        <!--</div>-->
                        <div class="col-sm-4 form-group"
                             ng-class="{'has-error' : submitted && branchForm.npi.$invalid, 'has-success' : submitted && branchForm.npi.$valid}">
                            <label for="npi">NPI</label>
                            <input type="text" name="npi" id="npi" class="form-control input-sm" ng-pattern="/^\d{10}$/"
                                   ng-disabled="!editing" ng-model="activeRecord.npi" ng-cloak>
                            <div ng-messages="branchForm.npi.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-4 form-group">
                            <label for="glAccount">GL Account</label>
                            <input type="text" name="glAccount" id="glAccount" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.glAccount">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group">
                            <label for="inClearingHouseId">Incoming Clearing House</label>
                            <select name="inClearingHouseId"
                                    id="inClearingHouseId"
                                    class="form-control input-sm chosen-select" ng-disabled="!editing"
                                    chosen="{allow_single_deselect: true}"
                                    ng-model="activeRecord.inClearingHouseId" ng-cloak
                                    ng-selected="activeRecord.inClearingHouseId === c.id"
                                    ng-options="c.id as c.name disable when !c.active for c in clearingHouses">
                            </select>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label for="outClearingHouseId">Outgoing Clearing House</label>
                            <select name="outClearingHouseId"
                                    id="outClearingHouseId"
                                    class="form-control input-sm chosen-select" ng-disabled="!editing"
                                    chosen="{allow_single_deselect: true}"
                                    ng-model="activeRecord.outClearingHouseId" ng-cloak
                                    ng-selected="activeRecord.outClearingHouseId === c.id"
                                    ng-options="c.id as c.name disable when !c.active for c in clearingHouses">
                            </select>
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.tag_line.$invalid, 'has-success' : submitted && branchForm.tag_line.$valid}">
                            <label for="tag_line">Service Facility Location (Box 32)</label>
                            <input type="text" name="tag_line" id="tag_line" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.tagLine" ng-cloak>
                            <div ng-messages="branchForm.tag_line.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Tagline is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="hide_service_facility_location" ng-disabled="!editing"
                                       ng-model="activeRecord.hideServiceFacilityLocation" ng-cloak><i></i> Hide Service
                                Facility Location (Box 32)
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.street_address.$invalid, 'has-success' : submitted && branchForm.street_address.$valid}">
                            <label for="street_address">Street</label>
                            <input type="text" name="street_address" id="street_address" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.streetAddress" ng-cloak>
                            <div ng-messages="branchForm.street_address.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Street is required.</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.city.$invalid, 'has-success' : submitted && branchForm.city.$valid}">
                            <label for="city">City</label>
                            <input type="text" name="city" id="city" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.city" ng-cloak>
                            <div ng-messages="branchForm.city.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">City is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.state.$invalid, 'has-success' : submitted && branchForm.state.$valid}">
                            <label for="state">State</label>
                            <div id="state-class-handler" class="select" ng-cloak>
                                <select name="state" id="state" chosen="{width: '240px'}"
                                        class="form-control input-sm chosen-select" ng-model="activeRecord.state"
                                        ng-disabled="!editing">
                                    <option ng-repeat="(key, data) in states" value="{{key}}"
                                            ng-selected="key === activeRecord.state">{{data}}
                                    </option>
                                </select>
                            </div>
                            <!--<div ng-messages="branchForm.state.$error" role="alert" ng-show="submitted">-->
                            <!--<div class="help-block" ng-message="required">State is required.</div>-->
                            <!--</div>-->
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.zipcode.$invalid, 'has-success' : submitted && branchForm.zipcode.$valid}">
                            <label for="zipcode">Zipcode</label>
                            <input type="text" name="zipcode" id="zipcode" class="form-control input-sm bfh-phone"
                                   required
                                   placeholder="#####-####" ng-pattern="/^(\d{5}(-\d{4})?)$/" ng-disabled="!editing"
                                   ng-model="activeRecord.zipcode" ng-cloak>
                            <div ng-messages="branchForm.zipcode.$error" role="alert" ng-show="submitted">
                                <!--<div class="help-block" ng-message="required">Zip is required.</div>-->
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                                <div class="help-block" ng-message="required">Zipcode is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.country.$invalid, 'has-success' : submitted && branchForm.country.$valid}">
                            <label for="country">Country (3)</label>
                            <input type="text" name="country" maxlength="3" id="country" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.country" ng-cloak>
                            <!--<div ng-messages="branchForm.street_address.$error" role="alert" ng-show="submitted">-->
                            <!--<div class="help-block" ng-message="required">Street is required.</div>-->
                            <!--</div>-->
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.phone_number.$invalid, 'has-success' : submitted && branchForm.phone_number.$valid}">
                            <label for="phone_number">Phone Number</label>
                            <input type="text"
                                   name="phone_number"
                                   id="phone_number"
                                   class="form-control input-sm"
                                   ui-mask-placeholder
                                   ui-mask-placeholder-char="_"
                                   ui-mask="(************* Ext. ?*?*?*?*"
                                   ui-options="{clearOnBlur: false}"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.phoneNumber"
                                   ng-cloak>
                            <div ng-messages="branchForm.phone_number.$error" role="alert" ng-show="submitted">
                                <!--<div class="help-block" ng-message="required">Phone number is required.</div>-->
                                <div class="help-block" ng-message="mask">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.fax_number.$invalid, 'has-success' : submitted && branchForm.fax_number.$valid}">
                            <label for="fax_number">Fax Number</label>
                            <input type="text"
                                   name="fax_number"
                                   id="fax_number"
                                   class="form-control input-sm"
                                   ui-mask-placeholder
                                   ui-mask-placeholder-char="_"
                                   ui-mask="(************* Ext. ?*?*?*?*"
                                   ui-options="{clearOnBlur: false}"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.faxNumber"
                                   ng-cloak>
                            <div ng-messages="branchForm.fax_number.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="mask">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-6 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="is_active" value="1"
                                       ng-disabled="!editing || !userService.isSuperAdmin()"
                                       ng-model="activeRecord.sendNotifications" ng-cloak><i></i> Send Patient
                                Text/Email Notification reminders?
                            </label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.poBox.$invalid, 'has-success' : submitted && branchForm.poBox.$valid}">
                            <label for="street_address">Patient Invoice PO BOX</label>
                            <input type="text"
                                   name="po_box"
                                   id="po_box"
                                   class="form-control input-sm"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.poBox"
                                   ng-cloak>
                        </div>
                        <div class="col-sm-6 form-group">
                            <label for="pay_online_url">Patient Invoice Pay Online URL</label>
                            <input type="text"
                                   name="po_box"
                                   id="pay_online_url"
                                   class="form-control input-sm"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.payOnlineUrl"
                                   ng-cloak>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.branchTaxIdToUse.$invalid,
                             'has-success' : submitted && branchForm.branchTaxIdToUse.$valid}">
                            <label for="branchTaxIdToUse">Branch Tax Id to Use</label>
                            <div id="branchTaxIdToUse" class="select" ng-cloak>
                                <select ng-model="activeRecord.taxIdType" name="branchTaxIdToUse" id="branchTaxIdType"
                                        class="form-control input-sm chosen-select" chosen required
                                        ng-disabled="!editing">
                                    <option value="leave_blank">Leave Blank</option>
                                    <option value="ssn">SSN</option>
                                    <option value="ein">EIN</option>
                                </select>
                                <div ng-messages="branchForm.branchTaxIdToUse.$error" role="alert" ng-show="submitted">
                                    <div class="help-block" ng-message="required">Branch Tax Id to Use is Required.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label for="branchTaxId">Branch Tax Id</label>
                            <input type="text" name="branchTaxId" id="branchTaxId" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.taxId" ng-cloak>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label for="other_id_1">Other ID 1</label>
                            <input type="text" name="other_id_1" id="other_id_1" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.otherId1" ng-cloak>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label for="other_id_2">Other ID 2</label>
                            <input type="text" name="other_id_2" id="other_id_2" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.otherId2" ng-cloak>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label for="projectedDeliveryOffset">Projected Delivery Offset</label>
                            <input type="number" name="projectedDeliveryOffset" id="projectedDeliveryOffset"
                                   class="form-control input-sm" ng-disabled="!editing"
                                   ng-model="activeRecord.projectedDeliveryOffset" ng-cloak maxlength="3">
                        </div>
                        <div class="col-sm-2 form-group">
                            <label for="monthlySalesGoal">Sales Goal</label>
                            <input type="number" name="monthlySalesGoal" id="monthlySalesGoal"
                                   class="form-control input-sm"
                                   placeholder="$0.00" ng-disabled="!editing"
                                   ng-model="activeRecord.monthlySalesGoal" ng-cloak>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label class="checkbox-inline checkbox-custom pt-5">
                                <input type="checkbox" name="useSalesTax" ng-disabled="!editing"
                                       ng-model="activeRecord.useSalesTax"
                                       ng-cloak><i></i>Use Sales Tax %
                            </label>
                            <span class="mt-10">
                                <input type="number" ng-disabled="!editing || !activeRecord.useSalesTax"
                                       ng-model="activeRecord.salesTax"
                                       class="form-control input-sm">
                            </span>
                        </div>
                        <div class="col-sm-4 form-group mt-25" ng-show="activeRecord.useSalesTax">
                            <label class="text-center text-danger">HCPCS must be re-saved in order for sales tax to
                                reload. Please re-save HCPCS in HCPCS selection.</label>
                        </div>
                        <div class="col-sm-4 form-group">
                            <label for="salesCalculationValue">Value to use when calculating sales tax</label>
                            <div>
                                <label class="btn btn-default" id="salesCalculationValue"
                                       ng-model="activeRecord.salesTaxCalculationValue"
                                       uib-btn-radio="'billable'"
                                       ng-disabled="!editing || !activeRecord.useSalesTax">Billable
                                </label>
                                <label class="btn btn-default" ng-model="activeRecord.salesTaxCalculationValue"
                                       uib-btn-radio="'allowable'"
                                       ng-disabled="!editing || !activeRecord.useSalesTax">Allowable
                                </label>
                            </div>
                        </div>
                        <div class="col-sm-4 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.purchaseOrderPrefix.$invalid, 'has-success' : submitted && branchForm.purchaseOrderPrefix.$valid}">
                            <label for="purchaseOrderPrefix">Purchase Order Prefix</label>
                            <input type="text" name="purchaseOrderPrefix" id="purchaseOrderPrefix" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.purchaseOrderPrefix" ng-cloak ng-maxlength="8" maxlength="8">
                            <div ng-messages="branchForm.purchaseOrderPrefix.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="maxlength">The PO prefix has a 8 character limit.</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox"
                                       value="0"
                                       ng-disabled="!editing || (editing && !authService.isSuperAdmin())"
                                       ng-model="activeRecord.useRealTimeRulesEngine"
                                       ng-cloak><i></i> Use Waystar Real Time Claim Edit Rules Engine?
                            </label>
                        </div>
                    </div>
                    <div class="row col-sm-12" ng-show="activeRecord.sendNotifications">
                        <i>"Please confirm your appointment with {{UserService.getCurrentCompany().name}}
                            ({{activeRecord.name}}), at 12:00PM on 01-01-2019 by clicking this link:
                            'www.test.com'. </i> <input type="text" name="additional_text" id="additional_text"
                                                        class="form-control input-sm" ng-disabled="!editing"
                                                        ng-model="activeRecord.additionalText" ng-cloak><i>"</i>

                    </div>
                    <div class="row">
                        <div class="col-sm-3 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="hide_company_logo" ng-disabled="!editing"
                                       ng-model="activeRecord.hideCompanyLogo" ng-cloak><i></i> Hide Logo on Print
                            </label>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="hide_company_name" ng-disabled="!editing"
                                       ng-model="activeRecord.hideCompanyName" ng-cloak><i></i> Hide Company Name on
                                Print
                            </label>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="useBranchLogo" ng-disabled="!editing"
                                       ng-model="activeRecord.useBranchLogo" ng-cloak><i></i> Use Branch Logo on Print
                                Screens
                            </label>
                        </div>
                        <div class="col-sm-3 form-group">
                            <label class="checkbox-inline checkbox-custom pt-25">
                                <input type="checkbox" name="useBranchName" ng-disabled="!editing"
                                       ng-model="activeRecord.useBranchName" ng-cloak><i></i> Use Branch Name (Box 31)
                            </label>
                        </div>
                    </div>
                    <legend class="form-section-header">Billing Company</legend>
                    <div class="row">
                        <div class="col-sm-6 form-group">
                            <label for="billing_company_name">Company Name (Box 33)</label>
                            <input type="text" name="billing_company_name" id="billing_company_name"
                                   class="form-control input-sm" ng-disabled="!editing"
                                   ng-model="activeRecord.billingCompanyName" ng-cloak>
                        </div>
                        <div class="col-sm-6 form-group"
                             ng-class="{'has-error' : submitted && branchForm.billing_company_npi.$invalid, 'has-success' : submitted && branchForm.billing_company_npi.$valid}">
                            <label for="billing_company_npi">NPI</label>
                            <input type="text" name="billing_company_npi" id="billing_company_npi"
                                   class="form-control input-sm" ng-pattern="/^\d{10}$/" ng-disabled="!editing"
                                   ng-model="activeRecord.billingNpi" ng-cloak>
                            <div ng-messages="branchForm.npi.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 form-group">
                            <label for="billing_street_address">Street</label>
                            <input type="text" name="billing_street_address" id="billing_street_address"
                                   class="form-control input-sm" ng-disabled="!editing"
                                   ng-model="activeRecord.billingStreetAddress" ng-cloak>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label for="billing_city">City</label>
                            <input type="text" name="billing_city" id="billing_city" class="form-control input-sm"
                                   ng-disabled="!editing" ng-model="activeRecord.billingCity" ng-cloak>
                        </div>
                        <div class="col-sm-2 form-group">
                            <label for="billing_state">State</label>
                            <div id="billing-state-class-handler" class="select" ng-cloak>
                                <select id="billing_state" chosen="{width: '240px'}"
                                        class="form-control input-sm chosen-select" ng-model="activeRecord.billingState"
                                        ng-disabled="!editing">
                                    <option ng-repeat="(key, data) in states" value="{{key}}"
                                            ng-selected="key === activeRecord.billingState">{{data}}
                                    </option>
                                </select>
                            </div>
                            <span id="billing-state-errors-container"></span>
                        </div>
                        <div class="col-sm-2 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.billing_zipcode.$invalid, 'has-success' : submitted && branchForm.billing_zipcode.$valid}">
                            <label for="billing_zipcode">Zipcode</label>
                            <input type="text" name="billing_zipcode" id="billing_zipcode" class="form-control input-sm"
                                   placeholder="#####-####" ng-pattern="/^(\d{5}(-\d{4})?)$/" ng-disabled="!editing"
                                   ng-model="activeRecord.billingZipcode" ng-cloak>
                            <div ng-messages="branchForm.billing_zipcode.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="pattern">Invalid format.</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-2 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.billing_phone_number.$invalid, 'has-success' : submitted && branchForm.billing_phone_number.$valid}">
                            <label for="billing_phone_number">Phone Number</label>
                            <input type="text"
                                   name="billing_phone_number"
                                   id="billing_phone_number"
                                   class="form-control input-sm"
                                   ui-mask-placeholder
                                   ui-mask-placeholder-char="_"
                                   ui-mask="(************* Ext. ?9?9?9?9?9"
                                   ui-options="{clearOnBlur: false}"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.billingPhoneNumber"
                                   ng-cloak>
                            <div ng-messages="branchForm.billing_phone_number.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="mask">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group"
                             ng-class="{ 'has-error' : submitted && branchForm.billing_fax_number.$invalid, 'has-success' : submitted && branchForm.billing_fax_number.$valid}">
                            <label for="billing_fax_number">Fax Number</label>
                            <input type="text"
                                   name="billing_fax_number"
                                   id="billing_fax_number"
                                   class="form-control input-sm"
                                   ui-mask-placeholder
                                   ui-mask-placeholder-char="_"
                                   ui-mask="(************* Ext. ?9?9?9?9?9"
                                   ui-options="{clearOnBlur: false}"
                                   ng-disabled="!editing"
                                   ng-model="activeRecord.billingFaxNumber"
                                   ng-cloak>
                            <div ng-messages="branchForm.billing_fax_number.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="mask">Invalid format.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group" ng-if="UserService.getCurrentCompany().useBranchTaxId"
                             ng-class="{ 'has-error' : submitted && branchForm.tax_id_type.$invalid, 'has-success' : branchForm && branchForm.tax_id_type.$valid}">
                            <label>Tax ID Type</label>
                            <div id="tax-id-to-use-class-handler" class="select" ng-cloak>
                                <select chosen ng-model="activeRecord.taxIdType" name="tax_id_type" id="tax_id_type"
                                        class="form-control input-sm chosen-select" ng-disabled="!editing">
                                    <option value=""></option>
                                    <option value="ssn">SSN</option>
                                    <option value="ein">EIN</option>
                                </select>
                            </div>
                            <div ng-messages="branchForm.tax_id_type.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Tax ID Type is required.</div>
                            </div>
                        </div>
                        <div class="col-sm-2 form-group" ng-if="UserService.getCurrentCompany().useBranchTaxId"
                             ng-class="{ 'has-error' : submitted && branchForm.tax_id.$invalid, 'has-success' : submitted && branchForm.tax_id.$valid}">
                            <label>Tax ID</label>
                            <input ng-model="activeRecord.taxId" type="text" name="tax_id" id="tax_id"
                                   class="form-control input-sm" ng-disabled="!editing" ng-cloak>
                            <div ng-messages="branchForm.tax_id.$error" role="alert" ng-show="submitted">
                                <div class="help-block" ng-message="required">Tax ID is required.</div>
                            </div>
                        </div>
                    </div>
                    <legend class="form-section-header">Branch Logo</legend>
                    <summernote id="branchLogo"
                                name="branchLogo"
                                class="note_field form-control input-sm no-resize"
                                height="200"
                                color="#fff"
                                editor="editor"
                                config="options"
                                ng-model="dto.branchLogo"></summernote>
                </form>
            </div>
        </div>
    </div>
</div>
