<form class="well" enctype="multipart/form-data" ng-submit="noteService.processUploadRecording()">
    <div>
        <div style="float:right;">
            <button type="submit" class="btn btn-rounded btn-sm btn-success" ng-disabled="noteService.treatingPractitionerId[prescription.id] === undefined || noteService.appointment[prescription.id].id === undefined || noteService.file === undefined"><i class="fa fa-save"></i>Save and Send Recording</button>
            <button class="btn btn-rounded btn-sm btn-danger" name="cancel"
                    ng-click="noteService.cancelUpload(prescription.id)">
                <i class="fa fa-times"></i>Cancel
            </button>
        </div>
        <input type="file"
               id="file" name="file"
               accept=".wav,.mp3,.m4a"
               ng-model="noteService.file"
               bind-custom-file
               callback-fn="noteService.addFiles(uploadFiles); $apply()"/>
    </div>
    <div class="mt-20">
        <div>
            <span ng-bind-html="noteService.file.name"></span>
            <div class="pull-right">
                <span>{{noteService.size}}</span><a ng-click="noteService.removeAll()" title="Remove from list to be uploaded"> <i class="fa fa-trash"></i></a>
            </div>
        </div>
        <progress style="width:400px;" value="{{noteService.file.loaded ? noteService.file.loaded : 0}}" max="{{noteService.file.size}}"></progress>
        <div class="row">
            <div id="spinner" ng-show="noteService.uploading">
                <i class="fa fa-spinner fa-spin fa-pulse"></i> Uploading...
            </div>
        </div>
    </div>
</form>