<div class="row mb-15">
    <div class="alert" id="note-alert-container" style="display: none;"></div>
    <button class="btn btn-cyan btn-sm btn-rounded text-sm pull-right" id="refresh-notes"
            ng-click="noteService.profileLoad(patientId)">
        <i class="fa fa-refresh" ng-class="{ 'fa-spin': (noteService.saving || noteService.noteLoading)}"></i>
        Refresh
    </button>
</div>
<div id="notes-view">
    <div class="panel-group" id="accordion" role="tablist">
        <div class="panel panel-primary"
             ng-repeat="prescription in noteService.prescriptionList | orderBy: ['-active', '-prescriptionDate']">
            <div class="panel-heading" id="heading-{{prescription.id}}" role="tab">
                <h4 class="panel-title">
                    <a data-parent="#accordion" data-target="#collapse-{{prescription.id}}" data-toggle="collapse"
                       role="button" ng-click="noteService.resetAudioSettings()">
                        {{ prescription.deviceType.name }} {{ prescription.id ? '(#'+prescription.id+')' : ""}}
                        {{prescription.active || prescription.deviceTypeId === 0 ? prescription.archived ? 'ARCHIVED' : ' ' : ' (Inactive) ' }}
                        <ng-repeat ng-repeat="sub in prescription.$subPrescriptions | unique: sub">
                            <i class="fa fa-angle-double-left"
                               ng-class="{'strikethrough text-orange' : !(sub.active || sub.deviceTypeId === 0)}"
                               title="{{ sub.deviceType.name }} {{ !(sub.active || sub.deviceTypeId === 0) ? ' [INACTIVE]' : '' }}"
                               ng-bind-html="' (#'+sub.id+')'"></i>
                            <i class="fa fa-exclamation-circle text-orange"
                               ng-if="!(sub.active || sub.deviceTypeId === 0)"></i>
                            <span class="badge bg-lightred pull-right"
                                  ng-bind-html="noteService.getNoteCount(noteService.prescriptionNotes[prescription.id].length, noteService.evaluationFormsByPrescriptionId[prescription.id].length, noteService.prescriptionNotes[sub.id].length)">
                            </span>
                        </ng-repeat>
                        <span class="badge bg-lightred pull-right" ng-if="prescription.$subPrescriptions === undefined || prescription.$subPrescriptions.length === 0"
                              ng-bind-html="noteService.getNoteCount(noteService.prescriptionNotes[prescription.id].length, noteService.evaluationFormsByPrescriptionId[prescription.id].length)">
                        </span>
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse" id="collapse-{{prescription.id}}" role="tabpanel">
                <!-- Thread Section -->
                <div class="panel-body pb-0">
                    <div class="row">
                        <div class="row" ng-repeat="type in noteService.noteTypes"
                             style="width:150px; display:inline-block; margin-left: 10px">
                            <input ng-change="noteService.filterType(prescription.id, type.lowerCaseName)"
                                   ng-model="noteService.checked[prescription.id][type.lowerCaseName]"
                                   style="margin-left:2%"
                                   type="checkbox"/>
                            <label>{{type.name}}</label>
                        </div>
                        <div class="row" style="width:150px; display:inline-block; margin-left: 10px">
                            <div>
                                <input ng-change="noteService.toggleCheckAllNoteTypes(prescription.id)"
                                       ng-model="noteService.enableAllNotes[prescription.id]" type="checkbox">

                                <label>Select All</label>
                            </div>
                        </div>
                        <br/>
                        <div class="row">
                            <div class="text-right ml-10 mt-10">
                                <button class="btn btn-primary btn-rounded btn-sm"
                                        data-target="#collapse-new-note-{{prescription.id}}"
                                        data-toggle="collapse"
                                        id="show-new-note-{{prescription.id}}"
                                        ng-click="noteService.cancelUpload(prescription.id);noteService.cancelRecord(prescription.id) "
                                        type="button"><i class="fa fa-file"></i>New Note
                                </button>
                                <button class="btn btn-success btn-rounded btn-sm"
                                        data-target="#collapse-ai-note-record-{{prescription.id}}"
                                        data-toggle="collapse"
                                        id="ai-note-{{prescription.id}}"
                                        ng-click="noteService.cancelNote(prescription.id); noteService.setUpForRecording()"
                                        ng-show="userService.hasPermission('ai_note_add') && noteService.noteType[prescription.id] === 'clinical' && noteService.canRecordAudio && noteService.aiNotesEnabled  && prescription.id != 0"
                                        type="button"><i class="fa fa-file-sound-o"></i> Start Recording
                                </button>
                                <button class="btn btn-rounded btn-sm btn-primary"
                                        data-target="#collapse-ai-note-upload-{{prescription.id}}"
                                        data-toggle="collapse"
                                        id="ai-note-upload-{{prescription.id}}"
                                        ng-click="noteService.cancelNote(prescription.id); noteService.removeAll()"
                                        ng-show="userService.hasPermission('ai_note_add') && noteService.noteType[prescription.id] === 'clinical' && noteService.aiNotesEnabled  && prescription.id != 0"
                                        type="button"><i class="fa fa-upload"></i> Upload Recording
                                </button>
                                <a class="btn btn-warning btn-rounded btn-sm" id="print-notes-{{prescription.id}}"
                                   ng-click="noteService.printPatientProfileNotes(prescription.id, patientId)"
                                   ng-show="noteService.hasNotes()">
                                    <i class="fa fa-print"></i> Print Notes</a>
                                <a class="btn btn-info btn-rounded btn-sm" id="clinical-notes"
                                   ng-click="noteService.openClinicalDocs(prescription.id)"><i
                                        class="fa fa-stethoscope"></i>Questionnaire</a>
                            </div>
                        </div>
                        <div class="row pt-15">
                            <div class="col-sm-1 col-sm-offset-11 text-center" style="padding-right: 22px !important;">
                                <div class="row">
                                    <label>All</label>
                                </div>
                                <div class="row">
                                    <input ng-change="noteService.toggleCheckAllNotes(prescription.id)"
                                           ng-disabled="noteService.disableCheckAllNotes[prescription.id]"
                                           ng-model="noteService.checkAllNotes[prescription.id]" type="checkbox">
                                </div>
                            </div>
                        </div>
                        <!-- Upload Audio Section -->
                        <div class="row pl-15">
                            <div class="col-sm-12">
                                <form class="ajax-crud-form" data-presc-id="{{prescription.id}}"
                                      name="ai-note-upload-form" enctype="multipart/form-data"
                                      ng-submit="noteService.processUploadRecording(prescription.id, patientId)">
                                    <div class="collapse-new-note collapse mb-10"
                                         id="collapse-ai-note-upload-{{prescription.id}}">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    <h3 class="custom-font ng-binding"><strong>Upload</strong> Audio
                                                    </h3>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    <div class="row">
                                                        <div class="col-sm-12"
                                                             id="appointment-ai-field-div-{{prescription.id}}">
                                                            <div class="form-group" ng-class="{'has-error' : noteService.noAppointment[prescription.id],
                                                            'has-success' : !noteService.noAppointment[prescription.id]}">
                                                                <div class="select"
                                                                     id="appointment-ai-class-handler-{{prescription.id}}">
                                                                    <select chosen disable-search="{{isMobile}}"
                                                                            class="appointment_field form-control input-sm chosen-select"
                                                                            ng-model="noteService.appointment[prescription.id]"
                                                                            ng-change="noteService.addAppointmentPractitionersToValidNoteTreatingPractitioners(prescription, noteService.appointment[prescription.id]); noteService.setDefaultNoteTreatingPractitioner(prescription, noteService.appointment[prescription.id])"
                                                                            ng-options="appointment as (appointmentService.getFormattedAppointmentDate(appointment) + (appointment.prescriptionId ? (' ' + utilService.prescriptionAbbreviation +' #' + appointment.prescriptionId) : '') + (appointment.$hasNote ? ' **Attached to Note' : '')) disable when (moment(appointment.startDateTime).isAfter(moment(), 'day')) for appointment in appointmentService.nonCancelledAppointments | filter:noteService.showAppointmentForNote(null, prescription) | orderBy:['-$hasNote','-startDateTime'] track by appointment.id"
                                                                    >
                                                                        <option value="">Select Appointment</option>
                                                                    </select>
                                                                    <div role="alert"
                                                                         ng-show="noteService.noAppointment[prescription.id]">
                                                                        <div class="help-block">
                                                                            Appointment is required to upload a
                                                                            clinical note
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-12 form-group"
                                                             id="treating-practitioner-ai-field-div-{{prescription.id}}">
                                                            <div id="treating-practitioner-ai-class-handler-{{prescription.id}}"
                                                                 class="select">
                                                                <select name="treating_practitioner_{{prescription.id}}"
                                                                        id="treating_practitioner_ai_{{prescription.id}}"
                                                                        class="treating_practitioner_field form-control input-sm chosen-select"
                                                                        ng-options="user.id as user.firstAndLastName for user in noteService.validNoteTreatingPractitioners[prescription.id]"
                                                                        ng-model="noteService.treatingPractitionerId[prescription.id]">
                                                                    <option value="">
                                                                        {{'Select Treating ' + utilService.practitionerNoun}}
                                                                    </option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br /><br />
                                                    <div class="form-group text-right">
                                                        <ng-include
                                                                src="'views/tmpl/notes/modal_upload_file.html'"></ng-include>
                                                    </div>
                                                    <div id="upload-alert-container" class="alert"
                                                         style="display: none;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- Record Audio Section -->
                        <div class="row pl-15">
                            <div class="col-sm-12">
                                <form class="ajax-crud-form" data-presc-id="{{prescription.id}}" name="ai-note-form"
                                      enctype="multipart/form-data"
                                      ng-submit="noteService.processAudioRecording(prescription.id, patientId)">
                                    <div class="collapse-new-note collapse mb-10"
                                         id="collapse-ai-note-record-{{prescription.id}}">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    <h3 class="custom-font ng-binding"><strong>Record</strong> Note</h3>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    <div class="row">
                                                        <div class="col-sm-12"
                                                             id="appointment-ai-record-field-div-{{prescription.id}}">
                                                            <div class="form-group" ng-class="{'has-error' : noteService.noAppointment[prescription.id],
                                                            'has-success' : !noteService.noAppointment[prescription.id]}">
                                                                <div class="select"
                                                                     id="appointment-ai-record-class-handler-{{prescription.id}}">
                                                                    <select chosen disable-search="{{isMobile}}"
                                                                            class="appointment_field form-control input-sm chosen-select"
                                                                            ng-model="noteService.appointment[prescription.id]"
                                                                            ng-change="noteService.addAppointmentPractitionersToValidNoteTreatingPractitioners(prescription, noteService.appointment[prescription.id]); noteService.setDefaultNoteTreatingPractitioner(prescription, noteService.appointment[prescription.id])"
                                                                            ng-options="appointment as (appointmentService.getFormattedAppointmentDate(appointment) + (appointment.prescriptionId ? (' ' + utilService.prescriptionAbbreviation +' #' + appointment.prescriptionId) : '') + (appointment.$hasNote ? ' **Attached to Note' : '')) disable when (moment(appointment.startDateTime).isAfter(moment(), 'day')) for appointment in appointmentService.nonCancelledAppointments | filter:noteService.showAppointmentForNote(null, prescription) | orderBy:['-$hasNote','-startDateTime'] track by appointment.id"
                                                                    >
                                                                        <option value="">Select Appointment</option>
                                                                    </select>
                                                                    <div role="alert"
                                                                         ng-show="noteService.noAppointment[prescription.id]">
                                                                        <div class="help-block">
                                                                            Appointment is required to record a
                                                                            clinical note
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-12 form-group"
                                                             id="treating-practitioner-ai-record-field-div-{{prescription.id}}">
                                                            <div id="treating-practitioner-ai-record-class-handler-{{prescription.id}}"
                                                                 class="select">
                                                                <select name="treating_practitioner_{{prescription.id}}"
                                                                        id="treating_practitioner_ai_record_{{prescription.id}}"
                                                                        class="treating_practitioner_field form-control input-sm chosen-select"
                                                                        ng-options="user.id as user.firstAndLastName for user in noteService.validNoteTreatingPractitioners[prescription.id]"
                                                                        ng-model="noteService.treatingPractitionerId[prescription.id]">
                                                                    <option value="">
                                                                        {{'Select Treating ' + utilService.practitionerNoun}}
                                                                    </option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br /><br />
                                                    <div class="row">
                                                        <button class="btn btn-rounded btn-sm btn-blue" type="button"
                                                                ng-click="noteService.startAudioRecording(prescription.id)" name="start" ng-disabled="noteService.disableStartButton || noteService.treatingPractitionerId[prescription.id] === undefined || noteService.appointment[prescription.id].id === undefined"><i class="fa fa-play"></i>Start
                                                        </button>
                                                        <button class="btn btn-rounded btn-sm btn-dutch" type="button"
                                                                ng-click="noteService.stopAudioRecording()" name="stop" ng-disabled="noteService.disableStopButton"><i class="fa fa-stop"></i>Stop</button>
                                                        <button class="btn btn-rounded btn-sm btn-warning" type="button"
                                                                ng-click="noteService.pauseAudioRecording()" name="pause" ng-disabled="noteService.disablePauseButton"><i class="fa fa-pause"></i>{{noteService.pauseContext}}</button>
                                                        <i ng-show="noteService.stopped">{{noteService.audioDisplay}} recording captured</i>
                                                        <div style="float:right;">
                                                            <button type="submit"
                                                                    class="btn btn-rounded btn-sm btn-success" ng-disabled="noteService.disableAudioSaveButton"><i class="fa fa-save"></i>Save
                                                                and Send
                                                            </button>
                                                            <button class="btn btn-rounded btn-sm btn-danger"
                                                                    name="cancel" type="button"
                                                                    ng-click="noteService.cancelRecord(prescription.id)">
                                                                <i class="fa fa-times"></i>Cancel
                                                            </button>
                                                        </div>
                                                        <div id="record-alert-container" class="alert"
                                                             style="display: none;"></div>
                                                    </div>
                                                    <div class="row">
                                                        <div id="spinner" ng-show="noteService.isAudioRecording">
                                                            <i ng-show="noteService.isAudioRecording" class="fa fa-spinner fa-spin fa-pulse"></i>Recording...
                                                        </div>
                                                        <div id="spinner-2" ng-show="noteService.isStopping">
                                                            <i ng-show="noteService.isStopping" class="fa fa-spinner fa-spin fa-pulse"></i>Processing...
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="row pl-15">
                            <div class="col-sm-12">
                                <form class="ajax-crud-form" data-presc-id="{{prescription.id}}" name="new-note-form">
                                    <div class="collapse-new-note collapse mb-10"
                                         id="collapse-new-note-{{prescription.id}}">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <div class="form-group" ng-class="{'has-error' : noteService.noNoteType[prescription.id],
                                                            'has-success' : !noteService.noNoteType[prescription.id]}">
                                                    <div class="select"
                                                         id="note-type-class-handler-{{prescription.id}}">
                                                        <select chosen disable-search="{{isMobile}}"
                                                                class="note_type_field form-control input-sm chosen-select"
                                                                ng-model="noteService.noteType[prescription.id]"
                                                                required>
                                                            <option value="">Select Note Type</option>
                                                            <option ng-if="userService.isBilling()" value="billing">
                                                                Billing
                                                            </option>
                                                            <option ng-if="userService.isBilling()" value="billing_ar">
                                                                Billing AR
                                                            </option>
                                                            <option ng-if="userService.isClinical() || userService.isCareExtender() || userService.isSuperAdmin()"
                                                                    value="clinical">
                                                                Clinical
                                                            </option>
                                                            <option value="complaint">Complaint</option>
                                                            <option value="general">General</option>
                                                            <option ng-if="prescription.id !== 0"
                                                                    value="patient_summary">
                                                                {{utilService.prescriptionAbbreviation}} Summary
                                                            </option>
                                                        </select>
                                                        <div role="alert"
                                                             ng-show="noteService.noNoteType[prescription.id]">
                                                            <div class="help-block">Note Type is required
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-8" id="template-field-div-{{prescription.id}}">
                                                <div class="form-group">
                                                    <div class="select" id="template-class-handler-{{prescription.id}}">
                                                        <select chosen disable-search="{{isMobile}}"
                                                                class="template_field form-control input-sm chosen-select"
                                                                ng-change="changeTemplate(prescription.id, patientId)"
                                                                ng-model="noteService.noteTemplateId[prescription.id]"
                                                                ng-options="template.id as template.name for template in noteService.templates | orderBy: 'name'"
                                                                placeholder-text-single="'Select Template'">
                                                            <option value="">Select Template</option>
                                                        </select>
                                                        <i class="fa fa-spinner fa-spin" ng-if="loadingTemplate"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12" id="appointment-field-div-{{prescription.id}}">
                                                <div class="form-group" ng-class="{'has-error' : noteService.noAppointment[prescription.id],
                                                            'has-success' : !noteService.noAppointment[prescription.id]}">
                                                    <div class="select"
                                                         id="appointment-class-handler-{{prescription.id}}">
                                                        <select chosen disable-search="{{isMobile}}"
                                                                class="appointment_field form-control input-sm chosen-select"
                                                                ng-model="noteService.appointment[prescription.id]"
                                                                ng-change="noteService.addAppointmentPractitionersToValidNoteTreatingPractitioners(prescription, noteService.appointment[prescription.id]); noteService.setDefaultNoteTreatingPractitioner(prescription, noteService.appointment[prescription.id])"
                                                                ng-options="appointment as (appointmentService.getFormattedAppointmentDate(appointment) + (appointment.prescriptionId ? (' ' + utilService.prescriptionAbbreviation +' #' + appointment.prescriptionId) : '') + (appointment.$hasNote ? ' **Attached to Note' : '')) disable when (moment(appointment.startDateTime).isAfter(moment(), 'day')) for appointment in appointmentService.nonCancelledAppointments | filter:noteService.showAppointmentForNote(null, prescription) | orderBy:['-$hasNote','-startDateTime'] track by appointment.id"
                                                        >
                                                            <option value="">Select Appointment</option>
                                                        </select>
                                                        <div role="alert"
                                                             ng-show="noteService.noAppointment[prescription.id]">
                                                            <div class="help-block">
                                                                Appointment is required to publish a clinical note
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" ng-if="(noteService.noteType[prescription.id] === 'clinical'
                                            || noteService.noteType[prescription.id] === 'crm')">
                                            <div class="col-sm-12 form-group"
                                                 id="treating-practitioner-field-div-{{prescription.id}}">
                                                <div id="treating-practitioner-class-handler-{{prescription.id}}"
                                                     class="select">
                                                    <select name="treating_practitioner_{{prescription.id}}"
                                                            id="treating_practitioner_{{prescription.id}}"
                                                            class="treating_practitioner_field form-control input-sm chosen-select"
                                                            ng-options="user.id as user.firstAndLastName for user in noteService.validNoteTreatingPractitioners[prescription.id]"
                                                            ng-model="noteService.treatingPractitionerId[prescription.id]">
                                                        <option value="">
                                                            {{'Select Treating ' + utilService.practitionerNoun}}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" ng-if="noteService.subjectRequired">
                                            <div class="col-sm-12 form-group" ng-class="{'has-error' : noteService.subjectRequired && noteService.noSubject[prescription.id],
                                                'has-success' : !noteService.subjectRequired || !noteService.noSubject[prescription.id]}">
                                                <label for="note-subject-{{prescription.id}}">Subject</label>
                                                <input type="text" id="note-subject-{{prescription.id}}"
                                                       required
                                                       maxlength="100"
                                                       class="form-control input-sm"
                                                       ng-model="noteService.subject[prescription.id]">
                                                <div role="alert"
                                                     ng-show="noteService.subjectRequired && noteService.noSubject[prescription.id]">
                                                    <div class="help-block">Subject is required</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="form-group" ng-class="{'has-error' : noteService.noBody[prescription.id],
                                                            'has-success' : !noteService.noBody[prescription.id]}">
                                                    <summernote class="note_field form-control input-sm no-resize"
                                                                color="#fff" config="summernoteOptions" editor="editor"
                                                                height="309" id="note_{{prescription.id}}"
                                                                required
                                                                ng-model="noteService.note[prescription.id]"
                                                                on-image-upload="upload(files)(prescription.id)"></summernote>
                                                    <div role="alert" ng-show="noteService.noBody[prescription.id]">
                                                        <div class="help-block">Note Body is required</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <button class="btn btn-rounded btn-sm btn-primary mr-3" name="save"
                                                        ng-click="noteService.saveNote(prescription, 'draft')">
                                                    Save As Draft
                                                </button>
                                                <button class="btn btn-rounded btn-sm btn-default" name="cancel"
                                                        ng-click="noteService.cancelNote(prescription.id)">
                                                    Cancel
                                                </button>
                                                <button class="btn btn-rounded btn-sm btn-success pull-right"
                                                        name="save"
                                                        ng-hide="noteService.noteType[prescription.id] === 'clinical'
                                                        && noteService.currentUserId !== noteService.treatingPractitionerId[prescription.id]"
                                                        ng-click="noteService.saveNote(prescription, 'publish')">
                                                    Save & Publish
                                                </button>
                                                <button class="btn btn-rounded btn-sm btn-success pull-right"
                                                        ng-click="noteService.saveNote(prescription, 'sign')"
                                                        ng-show="noteService.noteType[prescription.id] === 'clinical'
                                                        && noteService.isValidCareExtenderOrResident(null, prescription)">
                                                    Save & Sign
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="panel-footer">
                            <div class="row pl-15">
                                <div class="col-sm-12">
                                    <form class="ajax-crud-form" data-presc-id="{{prescription.id}}-2"
                                          name="evaluation-form"
                                          ng-show="prescription.id && userService.getCurrentCompany().advancedForms">
                                        <div class="panel-body ajax-crud-form">
                                            <h3 style="font-weight: bold;">Advanced Forms</h3>
                                            <div class="alert" id="evaluation-alert-container"
                                                 style="display: none;"></div>
                                            <div class="mb-10">
                                                <select ng-change="$state.goNewTab(formState, {prescriptionId: prescription.id, patientId: patientId}); formState = ''"
                                                        ng-model="formState"
                                                        ng-options="key as data disable when ['core.form.medical_screening','core.form.medical_screening_ped'].includes(key) for (key, data) in formTypes"></select>
                                            </div>
                                            <div class="filled bg-warning"
                                                 ng-hide="noteService.evaluationFormsByPrescriptionId[prescription.id].length">
                                                No evaluation added.
                                            </div>
                                            <table class="table table-condensed"
                                                   ng-show="noteService.evaluationFormsByPrescriptionId[prescription.id].length"
                                                   style="table-layout: fixed;">
                                                <thead class="bg-cyan">
                                                <tr>
                                                    <th>#</th>
                                                    <th>Form Name</th>
                                                    <th>Date</th>
                                                    <th>Appointment</th>
                                                    <th>Duplicated From</th>
                                                    <th></th>
                                                    <th></th>
                                                    <th></th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat="evaluationForm in noteService.evaluationFormsByPrescriptionId[prescription.id]">
                                                    <td>{{ evaluationForm.id }}</td>
                                                    <td>{{formTypes[evaluationForm.form]}}</td>
                                                    <td ng-if="evaluationForm.published == 1">
                                                        <span title="Created at:"><i class="fa fa-user-plus"></i> <i
                                                                class="fa fa-plus"></i>
                                                            {{moment.utc(evaluationForm.createdAt, 'YYYY-MM-DD HH:mm').local().format('YYYY-MM-DD HH:mm')}}</span>
                                                        <span title="Published at:"><i class="fa fa-book"></i> <i
                                                                class="fa fa-create"></i>
                                                            {{moment.utc(evaluationForm.updatedAt, 'YYYY-MM-DD HH:mm').local().format('YYYY-MM-DD HH:mm')}}</span>
                                                    </td>
                                                    <td ng-if="evaluationForm.published == 0">
                                                        <span title="Created at:"><i class="fa fa-user-plus"></i> <i
                                                                class="fa fa-plus"></i>
                                                            {{moment.utc(evaluationForm.createdAt, 'YYYY-MM-DD HH:mm').local().format('YYYY-MM-DD HH:mm')}}</span>
                                                        <span title="Updated at:"><i class="fa fa-user-plus"></i> <i
                                                                class="fa fa-create"></i>
                                                            {{moment.utc(evaluationForm.updatedAt, 'YYYY-MM-DD HH:mm').local().format('YYYY-MM-DD HH:mm')}}</span>
                                                    </td>
                                                    <td>
                                                        <span ng-bind-html="appointmentService.getFormattedAppointmentDateByID(evaluationForm.appointmentId)"></span>
                                                    </td>
                                                    <td class="text-center">{{ evaluationForm.duplicatedFromId ? '#' +
                                                            evaluationForm.duplicatedFromId : '-' }}
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-rounded btn-sm btn-success btn-block"
                                                                ng-click="evaluationFormService.duplicateAsDraft(evaluationForm)"
                                                                ng-if="evaluationForm.published == 1">
                                                            Duplicate as Draft
                                                        </button>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-rounded btn-sm btn-danger btn-block"
                                                                ng-click="evaluationFormService.deleteEvaluationForm(evaluationForm)"
                                                                ng-if="evaluationForm.published == 0">
                                                            Delete
                                                        </button>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-rounded btn-sm btn-primary btn-block"
                                                                ng-click="prescriptionService.viewEvaluationForm(evaluationForm)"
                                                                ng-if="evaluationForm.published == 1">View
                                                        </button>
                                                        <button class="btn btn-rounded btn-sm btn-cyan btn-block"
                                                                ng-click="prescriptionService.viewEvaluationForm(evaluationForm)"
                                                                ng-if="evaluationForm.published == 0">Edit
                                                        </button>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!-- End Add Note Section -->
                        <hr>
                        <div class="single-note media mt-0 p-10 b-{{noteService.noteTypes[note.noteType]['color']}}"
                             ng-repeat="note in noteService.prescriptionNotes[prescription.id] | filter: noteService.typeFilter"
                             ng-show="noteService.prescriptionNotes[prescription.id].length">
                            <div class="col-sm-1 pl-0" ng-if="note.parentId"></div>
                            <div class="col-sm-1 pl-0">
                                <div class="thumb thumb-md" ng-click="noteService.viewNote(note)">
                                    <img alt=""
                                         class="img-circle size-50x50"
                                         id="user-profile-photo"
                                         onerror="return false;" profile-photo
                                         user-id="{{note.userId}}">
                                </div>
                            </div>
                            <div ng-class="!note.parentId ? 'col-sm-10 pl-0' : 'col-sm-9 pl-0'">
                                <div class="col-sm-12">
                                    <div class="row" ng-click="noteService.viewNote(note)">
                                        <span class="full-note col-sm-10 pl-0" style="font-weight: bold;">
                                            {{ noteService.subjectRequired ? utilService.cleanHTML(note.subject) :
                                                utilService.cleanHTML(note.note)}}
                                        </span>
                                        <div class="note-type hidden-xs label pull-right bg-{{noteService.noteTypes[note.noteType]['color']}}"
                                             ng-bind-html="noteService.noteTypes[note.noteType]['name']"></div>
                                        <div class="note-type hidden-xs label pull-right bg-danger"
                                             ng-bind-html="'Draft'" ng-if="!note.published"
                                             style="margin-right: 5px; !important"></div>
                                        <div class="note-type hidden-xs label pull-right bg-warning"
                                             ng-bind-html="'AI Note'" ng-if="note.transcriptionDetailId"
                                             style="margin-right: 5px; !important"></div>
                                    </div>
                                    <div class="row" ng-if="note.parentId">
                                        <div class="note-type hidden-xs label pull-right bg-{{noteService.childTypes[note.childType]['color']}}"
                                             ng-bind-html="noteService.childTypes[note.childType]['name']"></div>
                                    </div>
                                    <div class="row">
                                        <small class="posted-by text-primary col-sm-3 pl-0"
                                               ng-click="noteService.viewNote(note, false)">
                                            {{ utilService.formatName(note.user, 'FLMC') }}
                                        </small>
                                        <span class="col-sm">
                                            <button class="btn btn-default btn-rounded btn-xs addendum"
                                                    id="add-correction-{{note.id}}"
                                                    ng-click="noteService.viewNote(note, true)"
                                                    ng-if="noteService.showCorrectionButton(note)"
                                                    type="button">
                                                <i class="fa fa-pencil"></i>Correction
                                            </button>
                                        </span>
                                        <span class="pull-right text-sm text-muted" title="Creation Date">
                                            <i class="fa fa-user-plus"></i> <i class="fa fa-plus"></i>
                                            {{moment.utc(note.createdAt, 'YYYY-MM-DD HH:mm:ss').local().format('L')}}
                                        </span>
                                        <span class="pr-10 pull-right text-sm text-muted" title="Updated Date">
                                            <i class="fa fa-user-plus"></i> <i class="fa fa-create"></i>
                                            {{moment.utc(note.updatedAt, 'YYYY-MM-DD HH:mm:ss').local().format('L')}}
                                        </span>
                                    </div>
                                    <div class="row" ng-if="note.publishedAt">
                                        <span class="pull-right text-sm text-muted" title="Published Date">
                                            <i class="fa fa-book"></i> <i class="fa fa-create"></i>
                                            {{ dateService.getFormattedTS(note.publishedAt) }}
                                        </span>
                                    </div>
                                    <div class="row">
                                        <span class="pull-right text-sm text-muted"
                                              title="Appointment Attached Date"
                                              ng-if="note.appointment.startDateTime">
                                            <i class="fa fa-user"></i>   <i class="fa fa-calendar"></i>
                                            {{moment(note.appointment.startDateTime).format('L')}}
                                        </span>
                                        <span class="pull-right text-sm text-muted"
                                              ng-if="!note.appointment.startDateTime">
                                            <i class="fa fa-user"></i>   <i class="fa fa-calendar"></i>
                                            No attached appt.
                                        </span>
                                        <span class="pr-10 pull-right text-sm text-muted"
                                              title="Appointment Attached Type"
                                              ng-if="note.appointment.appointmentType">
                                            <i class="fa fa-user"></i>   <i class="fa fa-calendar"></i>
                                            {{ note.appointment.appointmentType.name }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-1 text-center">
                                <div class="row">
                                    <label>Print</label>
                                </div>
                                <div class="row">
                                    <input ng-change="noteService.checkForPrint(note)" ng-model="note.$checked"
                                           type="checkbox">
                                </div>
                            </div>
                        </div>
                        <div ng-repeat="sub in prescription.$subPrescriptions">
                            <div class="single-note media mt-0 p-10 b-{{noteService.noteTypes[note.noteType]['color']}}"
                                 ng-repeat="note in noteService.prescriptionNotes[sub.id] | filter: noteService.typeFilter"
                                 ng-show="noteService.prescriptionNotes[sub.id].length">
                                <div class="col-sm-1 pl-0" ng-if="note.parentId"></div>
                                <div class="col-sm-1 pl-0">
                                    <div class="thumb thumb-md" ng-click="noteService.viewNote(note)">
                                        <img alt=""
                                             class="img-circle size-50x50"
                                             id="user-profile-photo-{{sub.id}}"
                                             onerror="return false;" profile-photo
                                             user-id="{{note.userId}}">
                                    </div>
                                </div>
                                <div ng-class="!note.parentId ? 'col-sm-10 pl-0' : 'col-sm-9 pl-0'">
                                    <div class="col-sm-12">
                                        <div class="row" ng-click="noteService.viewNote(note)">
                                        <span class="full-note col-sm-10 pl-0" style="font-weight: bold;">
                                            <i class="fa fa-angle-double-left"
                                               ng-class="{'strikethrough text-orange' : !(sub.active || sub.deviceTypeId === 0)}"
                                               title="{{ sub.deviceType.name }} {{ !(sub.active || sub.deviceTypeId === 0) ? ' [INACTIVE]' : '' }}"
                                               ng-bind-html="' (#'+sub.id+')'"></i>
                                            {{ noteService.subjectRequired ? utilService.cleanHTML(note.subject) :
                                                utilService.cleanHTML(note.note)}}
                                        </span>
                                            <div class="note-type hidden-xs label pull-right bg-{{noteService.noteTypes[note.noteType]['color']}}"
                                                 ng-bind-html="noteService.noteTypes[note.noteType]['name']"></div>
                                            <div class="note-type hidden-xs label pull-right bg-danger"
                                                 ng-bind-html="'Draft'" ng-if="!note.published"
                                                 style="margin-right: 5px; !important"></div>
                                        </div>
                                        <div class="row" ng-if="note.parentId">
                                            <div class="note-type hidden-xs label pull-right bg-{{noteService.childTypes[note.childType]['color']}}"
                                                 ng-bind-html="noteService.childTypes[note.childType]['name']"></div>
                                        </div>
                                        <div class="row">
                                            <small class="posted-by text-primary col-sm-3 pl-0"
                                                   ng-click="noteService.viewNote(note, false)">
                                                {{ utilService.formatName(note.user, 'FLMC') }}
                                            </small>
                                            <span class="col-sm">
                                            <button class="btn btn-default btn-rounded btn-xs addendum"
                                                    id="add-correction-{{note.id}}-{{sub.id}}"
                                                    ng-click="noteService.viewNote(note, true)"
                                                    ng-if="noteService.showCorrectionButton(note)"
                                                    type="button">
                                                <i class="fa fa-pencil"></i>Correction
                                            </button>
                                        </span>
                                            <span class="pull-right text-sm text-muted" title="Creation Date">
                                            <i class="fa fa-user-plus"></i> <i class="fa fa-plus"></i>
                                            {{moment.utc(note.createdAt, 'YYYY-MM-DD HH:mm:ss').local().format('L')}}
                                        </span>
                                            <span class="pr-10 pull-right text-sm text-muted" title="Updated Date">
                                            <i class="fa fa-user-plus"></i> <i class="fa fa-create"></i>
                                            {{moment.utc(note.updatedAt, 'YYYY-MM-DD HH:mm:ss').local().format('L')}}
                                        </span>
                                        </div>
                                        <div class="row" ng-if="note.publishedAt">
                                        <span class="pull-right text-sm text-muted" title="Published Date">
                                            <i class="fa fa-book"></i> <i class="fa fa-create"></i>
                                            {{ dateService.getFormattedTS(note.publishedAt) }}
                                        </span>
                                        </div>
                                        <div class="row">
                                        <span class="pull-right text-sm text-muted"
                                              title="Appointment Attached Date"
                                              ng-if="note.appointment.startDateTime">
                                            <i class="fa fa-user"></i>   <i class="fa fa-calendar"></i>
                                            {{moment(note.appointment.startDateTime).format('L')}}
                                        </span>
                                            <span class="pull-right text-sm text-muted"
                                                  ng-if="!note.appointment.startDateTime">
                                            <i class="fa fa-user"></i>   <i class="fa fa-calendar"></i>
                                            No attached appt.
                                        </span>
                                            <span class="pr-10 pull-right text-sm text-muted"
                                                  title="Appointment Attached Type"
                                                  ng-if="note.appointment.appointmentType">
                                            <i class="fa fa-user"></i>   <i class="fa fa-calendar"></i>
                                            {{ note.appointment.appointmentType.name }}
                                        </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-1 text-center">
                                    <div class="row">
                                        <label>Print</label>
                                    </div>
                                    <div class="row">
                                        <input ng-change="noteService.checkForPrint(note)" ng-model="note.$checked"
                                               type="checkbox">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="filled bg-warning" ng-hide="noteService.prescriptionNotes[prescription.id].length">
                        No notes found.
                    </div>
                </div>
                <!-- End Thread Section -->
            </div>
        </div>
    </div>
</div>
