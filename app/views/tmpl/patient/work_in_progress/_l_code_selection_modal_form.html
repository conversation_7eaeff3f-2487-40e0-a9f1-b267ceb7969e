<div class="modal-content" modal-movable>
    <ng-include src="'views/tmpl/patient/purchasing/_purchasing_inventory_modal.html'"></ng-include>
    <div class="modal-header">
        <h4 class="modal-title">HCPCS Selection</h4>
    </div>
    <div class="modal-body">
        <div class="row" ng-if="loading">
            <div class="col-sm-12 text-center">
                <i class="fa fa-spinner fa-spin fa-5x" ng-style="{ 'color': '#16A085' }"></i>
            </div>
        </div>
        <div class="row" ng-if="!loading">
            <form class="col-sm-12 ajax-crud-form">
                <div class="alert"
                     id="alert-container"
                     style="display: none;"></div>
                <div class="row" ng-show="checked.showTree">
                    <div class="col-sm-6">
                        <div class="panel panel-primary mb-0 pt-10 pb-10" style="max-height: 445px; overflow: auto;">
                            <div class="panel-content">
                                <abn-tree expand-level="1"
                                          on-select="selectNode(branch)"
                                          tree-control="my_tree"
                                          tree-data="lCodeNodeTree"></abn-tree>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="col-sm-12 custom-table" data-height="460">
                            <div class="col-sm-12 header">
                                <div class="col-sm-1"></div>
                                <div class="col-sm-2 p-0">HCPCS</div>
                                <div class="col-sm-9 p-0">Description</div>
                            </div>
                            <div class="col-sm-12 custom-table-body" style="height: 425px;">
                                <div class="col-sm-12" ng-repeat="l in nodelCodes | orderBy: 'name'">
                                    <div class="col-sm-1">
                                        <i class="fa fa-plus text-success"
                                           ng-click="addLCode(l)"></i>
                                    </div>
                                    <div class="col-sm-2 p-0" ng-bind-html="l.name"></div>
                                    <div class="col-sm-9 p-0" ng-bind-html="l.description"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label>The following HCPCS Codes have been selected for this patient:</label>
                    </div>
                    <div class="col-sm-6">
                        <label class="checkbox checkbox-custom pull-right" ng-if="lCodeNodeTree.length">
                            <input ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                   ng-model="checked.showTree"
                                   type="checkbox">
                            <i></i>Show Tree
                        </label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <input autocomplete="off"
                                   class="form-control input-sm text-uppercase"
                                   ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                   ng-model="lCode"
                                   placeholder="Enter a HCPCS Code..."
                                   size="40"
                                   type="text"
                                   typeahead-append-to-body="true"
                                   typeahead-min-length="2"
                                   typeahead-on-select="addLCode($item, $model, $label)"
                                   typeahead-template-url="l-code-selection-template.html"
                                   typeahead-wait-ms="500"
                                   uib-typeahead="selected as lCode.name+' - '+lCode.description for lCode in refreshLCodes($viewValue)"/>
                        </div>
                    </div>
                </div>
                <div class="row filled bg-error text-center mb-10" ng-show="missingFee">
                    <div class="col-xs-12 text-center">
                        This HCPCS Code, {{lCodeName}}, cannot be used until a fee has been established for it.
                        <span class="pull-right" ng-click="exitAlertNotice()">&times;</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 custom-table" data-height="299" id="procedure-code-list">
                        <div class="row pl-10">
                            <div class="col-sm-12">
                                <div class="col-sm-1 p-0">HCPCS</div>
                                <div class="col-sm-1 p-0">Qty</div>
                                <div class="col-sm-1 p-0">Mod 1</div>
                                <div class="col-sm-1 p-0">Mod 2</div>
                                <div class="col-sm-1 p-0">Mod 3</div>
                                <div class="col-sm-1 p-0">Mod 4</div>
                                <div class="col-sm-1 p-0">Rental</div>
                                <div class="col-sm-2 p-0" ng-show="anyRented">Service Start</div>
                                <div class="col-sm-2 p-0" ng-show="anyRented">Service End</div>
                                <div class="col-sm-1 p-0"></div>
                            </div>
                        </div>
                        <div class="col-sm-12 custom-table-body" style="height: 512px;">
                            <div class='wrapper'>
                                <div class="dragDiv" dragular="dragularOptions" dragular-model="prescriptionLCodes">
                                    <div class="row mb-5 pl-10" ng-repeat="prescriptionLCode in prescriptionLCodes track by $index"
                                         style="border-bottom: 2px solid lightgrey">
                                        <div class="row">
                                            <div class="col-sm-1">
                                                <div class="col-sm-1 pt-10 handle"
                                                     ng-bind-html="prescriptionLCode.lCode.name"></div>
                                            </div>
                                            <div class="col-sm-1 p-0">
                                                <div class="form-group m-0 pr-5">
                                                    <input class="form-control input-sm p-5"
                                                           min="1"
                                                           name="quantity"
                                                           ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                                           ng-model="prescriptionLCode.quantity"
                                                           required
                                                           step="1"
                                                           type="number">
                                                </div>
                                            </div>
                                            <div class="col-sm-1 p-0">
                                                <div class="form-group m-0 pr-5">
                                                    <select style="width: 80px;" class="input-sm focusable-dropdown"
                                                            id="mod1_{{$index}}"
                                                            ng-cloak="{{loading}}"
                                                            ng-model="prescriptionLCode.modifier1"
                                                            ng-options="modifier for modifier in modifiersList"
                                                            data-placeholder="">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-sm-1 p-0">
                                                <div class="form-group m-0 pr-5">
                                                    <select style="width: 80px;" class="input-sm focusable-dropdown"
                                                            id="mod2_{{$index}}"
                                                            ng-cloak="{{loading}}"
                                                            ng-model="prescriptionLCode.modifier2"
                                                            ng-options="modifier for modifier in modifiersList"
                                                            data-placeholder="">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-sm-1 p-0">
                                                <div class="form-group m-0 pr-5">
                                                    <select style="width: 80px;" class="input-sm focusable-dropdown"
                                                            id="mod3_{{$index}}"
                                                            ng-cloak="{{loading}}"
                                                            ng-model="prescriptionLCode.modifier3"
                                                            ng-options="modifier for modifier in modifiersList"
                                                            data-placeholder="">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-sm-1 p-0">
                                                <div class="form-group m-0 pr-5">
                                                    <select style="width: 80px;" class="input-sm focusable-dropdown"
                                                            id="mod4_{{$index}}"
                                                            ng-cloak="{{loading}}"
                                                            ng-model="prescriptionLCode.modifier4"
                                                            ng-options="modifier for modifier in modifiersList"
                                                            data-placeholder="">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-sm-1 p-0">
                                                <div class="form-group ml-10 mt-10">
                                                    <input ng-click="toggleRental()"
                                                           ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                                           ng-model="prescriptionLCode.$isRental"
                                                           type="checkbox">
                                                </div>
                                            </div>
                                            <div class="col-sm-2 p-0">
                                                <div class="form-group m-0 pr-5"
                                                     ng-show="prescriptionLCode.$isRental">
                                                    <div class="input-group">
                                                        <input as-date
                                                               class="form-control input-sm"
                                                               close-text="Close"
                                                               datepicker-options="calendar.dateOptions"
                                                               is-open="calendar.opened['start'+$index]"
                                                               ng-cloak
                                                               ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                                               ng-model="prescriptionLCode.dateOfService"
                                                               ng-required="prescriptionLCode.$isRental"
                                                               type="text"
                                                               uib-datepicker-popup="MM/dd/yyyy">
                                                        <span class="input-group-btn">
                                                            <button class="btn btn-default btn-sm"
                                                                    ng-click="calendar.open($event, 'start'+$index)"
                                                                    type="button"><i
                                                                    class="fa fa-calendar"></i></button>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-2 p-0">
                                                <div class="form-group m-0 pr-5"
                                                     ng-show="prescriptionLCode.$isRental">
                                                    <div class="input-group">
                                                        <input as-date
                                                               class="form-control input-sm"
                                                               close-text="Close"
                                                               datepicker-options="calendar.dateOptions"
                                                               is-open="calendar.opened['end'+$index]"
                                                               ng-cloak
                                                               ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                                               ng-model="prescriptionLCode.dateOfServiceEnd"
                                                               ng-required="prescriptionLCode.$isRental"
                                                               type="text"
                                                               uib-datepicker-popup="MM/dd/yyyy">
                                                        <span class="input-group-btn">
                                                            <button class="btn btn-default btn-sm"
                                                                    ng-click="calendar.open($event, 'end'+$index)"
                                                                    type="button"><i
                                                                    class="fa fa-calendar"></i></button>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-1 p-0"
                                                 ng-hide="prescriptionService.prescriptionDateInLockedBillingCycle || prescriptionLCode.$noDelete">
                                                <a class="action-link text-danger text-sm pull-right"
                                                   id="remove_{{prescriptionLCode.lCode.name}}"
                                                   ng-click="removeLCode(prescriptionLCode.orderNum)"
                                                   role="button">
                                                    <i class="fa fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 text-nowrap pt-10 pb-10">
                                                <div class="col-sm-12">
                                                    <span class="tooltips handle"
                                                          data-placement="top"
                                                          data-toggle="tooltip"
                                                          title="{{prescriptionLCode.lCode.description}}">
                                                        {{prescriptionLCode.lCode.description}}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 form-group">
                                                <div class="col-sm-1 mt-10">
                                                    Item{{prescriptionLCode.quantity == 1 ? '' : 's'}}
                                                </div>
                                                <div class="col-sm-9">
                                                    <ui-select close-on-select="false"
                                                               id="itemPhysical{{prescriptionLCode.orderNum}}"
                                                               multiple
                                                               ng-change="plcSelectionChanged(prescriptionLCode)"
                                                               ng-model="plcPhysicalItems[prescriptionLCode.orderNum - 1].items"
                                                               theme="bootstrap">
                                                        <ui-select-match placeholder="Select from list"
                                                                         style="background-color: {{$item.insuranceVerificationLCodeId ? '#b9f6ca' : '#f7e1b5'}}"
                                                                         title="{{$item.item.name}}">
                                                            {{$item.item.name + ($item.serialNumber ? (' (' + $item.serialNumber + ')') : '')}}
                                                        </ui-select-match>
                                                        <ui-select-choices
                                                                repeat="item in getUnselectedItems()">
                                                            <div ng-bind-html="item.item.name | highlight: $select.search"></div>
                                                            <small>{{'ID: ' + item.id + ' P/N: ' + item.item.partNumber + (item.serialNumber ? (' S/N: `' + item.serialNumber + '`') : '')}}</small>
                                                        </ui-select-choices>
                                                    </ui-select>
                                                </div>
                                                <div class="col-sm-2 pr-10">
                                                    <button class="btn btn-sm btn-rounded btn-warning pull-right"
                                                            ng-click="openInventoryModal(prescriptionLCode.id)"
                                                            ng-disabled="loading">
                                                        <i class="fa fa-search"></i> Search Inventory
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6" title="Release unselected Physical Items to inventory">
                        <label class="checkbox checkbox-custom" ng-if="getUnselectedItems().length">
                            <input ng-model="checked.releaseUnselectedItems"
                                   type="checkbox">
                            <i></i>Release Inventory
                        </label>
                    </div>
                    <div class="col-sm-6">
                        <label class="checkbox checkbox-custom ml-10 pull-right"
                               ng-if="userService.hasPermission('common_procedure_add')">
                            <input ng-disabled="prescriptionService.prescriptionDateInLockedBillingCycle"
                                   ng-model="checked.saveCommonProcedure"
                                   type="checkbox">
                            <i></i>
                            Save as HCPCS Template
                        </label>
                        <input class="input input-sm mt-5 pull-right"
                               ng-hide="!checked.saveCommonProcedure"
                               ng-model="commonProcedure.title"
                               placeholder="Procedure Name"
                               type="text">
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <button class="btn btn-warning btn-rounded btn-sm"
                                id="apply-all"
                                ng-click="applyAll()"
                                type="button">Apply All
                        </button>
                        <button class="btn btn-default btn-rounded btn-sm pull-right"
                                data-dismiss="modal"
                                ng-click="$dismiss()"
                                type="button">Cancel
                        </button>
                        <button class="btn btn-primary btn-rounded btn-sm ml-10 mr-10 pull-right"
                                data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Saving..."
                                id="save-prescription-l-codes"
                                ng-click="savePrescriptionLCodes()"
                                ng-disabled="disableSaveButton"
                                type="button">Save
                        </button>
                        <button class="btn btn-success btn-rounded btn-sm pull-right"
                                id="select-common-procedure"
                                ng-click="openCommonProcedures()"
                                ng-if="!prescriptionService.prescriptionDateInLockedBillingCycle"
                                type="button">
                            Select HCPCS Template
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script id="l-code-selection-template.html" type="text/ng-template">
    <a>
        <div class="row">
            <div class="col-sm-12">
                {{ match.label }}
            </div>
        </div>
    </a>
</script>
