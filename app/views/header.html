<header class="clearfix">
        <nymbl-timeout></nymbl-timeout>

    <!-- Branding -->
    <div class="branding {{main.settings.brandingColor}}">
        <a class="brand" ui-sref="app.dashboard">
            <span><strong>nymbl.</strong>systems</span>
        </a>
        <a href="javascript:" class="offcanvas-toggle visible-xs-inline" offcanvas-sidebar>
            <i class="fa fa-bars"></i>
        </a>
    </div>
    <!-- Branding end -->

    <!-- Left-side navigation -->
    <ul class="nav-left pull-left list-unstyled list-inline">
        <li class="sidebar-collapse divided-right">
            <a href="javascript:" collapse-sidebar>
                <i class="fa fa-outdent"></i>
            </a>
        </li>
        <!--<li class="dropdown divided-right settings" uib-dropdown>-->
        <!--<a href class="dropdown-toggle" uib-dropdown-toggle>-->
        <!--<i class="fa fa-cog"></i>-->
        <!--</a>-->
        <!--<ul class="dropdown-menu with-arrow animated littleFadeInUp" role="menu">-->
        <!--<li>-->
        <!--<div class="form-group ng-scope">-->
        <!--<div class="row">-->
        <!--<label class="col-xs-8 control-label">Fixed header</label>-->
        <!--<div class="col-xs-4 control-label">-->
        <!--<div class="onoffswitch lightred small" ng-click="$event.stopPropagation()">-->
        <!--<input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox"-->
        <!--id="fixed-header" checked="" ng-model="main.settings.headerFixed">-->
        <!--<label class="onoffswitch-label" for="fixed-header">-->
        <!--<span class="onoffswitch-inner"></span>-->
        <!--<span class="onoffswitch-switch"></span>-->
        <!--</label>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--</li>-->

        <!--<li>-->
        <!--<div class="form-group ng-scope">-->
        <!--<div class="row">-->
        <!--<label class="col-xs-8 control-label">Fixed aside</label>-->
        <!--<div class="col-xs-4 control-label">-->
        <!--<div class="onoffswitch lightred small" ng-click="$event.stopPropagation()">-->
        <!--<input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox"-->
        <!--id="fixed-aside" checked="" ng-model="main.settings.asideFixed">-->
        <!--<label class="onoffswitch-label" for="fixed-aside">-->
        <!--<span class="onoffswitch-inner"></span>-->
        <!--<span class="onoffswitch-switch"></span>-->
        <!--</label>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--</li>-->
        <!--</ul>-->
        <!--</li>-->
    </ul>
    <!-- Left-side navigation end -->

    <!-- Search -->
    <div class="search" id="main-search">
        <div id="scrollable-dropdown-menu">
            <input type="text"
                   class="input-sm form-control underline-input"
                   placeholder="Search Patient by Name, Id, SSN, DOB (MMDDYYYY) or Phone"
                   uib-typeahead="selected as utilService.formatNameWithDOB(patient) for patient in getPatients($viewValue)"
                   ng-model="selected"
                   typeahead-on-select="selectPatient($item); selected=undefined;"
                   typeahead-min-length="2"
                   typeahead-wait-ms="500"
                   typeahead-template-url="patientWithDOBSearchTemplate.html"/>
        </div>
    </div>
    <!-- Search end -->

    <!-- Right-side navigation -->
    <ul class="nav-right pull-right list-inline">
        <!--        <li>-->
        <!--            <a class="bg-danger" href ng-click="systemSlowness()">-->
        <!--                <i class="fa fa-info-circle"></i> System slowness issues-->
        <!--            </a>-->
        <!--        </li>-->
<!--        <li>-->
<!--            <a class="bg-info" href ng-click="systemMaintenance()">-->
<!--                <i class="fa fa-info-circle"></i> April Webinars and Office Hours Schedule!-->
<!--            </a>-->
<!--        </li>-->
        <li ng-if="announcementService.announcement && announcementService.announcement.showInHeader">
            <a ng-style="{'background-color': announcementService.announcement.color}" style="color: white;" href
               ng-click="announcementService.openAnnouncementModal(announcementService.announcement)">
                <i class="fa fa-info-circle"></i> {{ announcementService.announcement.name }}
            </a>
        </li>
        <li class="dropdown notifications" uib-dropdown>
            <!--<a href class="dropdown-toggle" uib-dropdown-toggle>-->
            <a href ng-click="main.settings.rightbarShow = !main.settings.rightbarShow">
                <i class="fa fa-bell" ng-hide="userNotificationService.loading"></i>
                <span class="badge bg-lightred"
                      id="notification-counter-badge"
                      ng-show="userNotificationService.getNotifications({hasRead: false}).length && !userNotificationService.loading"
                      ng-bind-html="userNotificationService.getNotifications({hasRead: false}).length"></span>
                <i class="fa fa-spinner fa-spin" ng-show="userNotificationService.loading"></i>
            </a>
            <!--<div class="dropdown-menu pull-right with-arrow panel panel-default animated littleFadeInLeft" role="menu">-->
            <!--<div class="panel-heading">-->
            <!--You have <strong ng-bind-html="unreadNotifications.length ? unreadNotifications.length : '0'"></strong> notifications-->
            <!--</div>-->
            <!--<ul class="list-group">-->
            <!--<li class="list-group-item" ng-repeat="un in unreadNotifications">-->
            <!--<div class="notification-li media" ng-show="unreadNotifications.length">-->
            <!--<div class="media-left" style="padding: 10px;"-->
            <!--ng-click="un.notification.patientId ? $state.go('app.patient.profile', {patientId : un.notification.patientId}) : null">-->
            <!--<div class="thumb thumb-sm">-->
            <!--<img patient-id="{{un.notification.patientId}}"-->
            <!--id="not-profile-photo"-->
            <!--class="img-circle size-30x30"-->
            <!--profile-photo onerror="return false;"-->
            <!--alt="">-->
            <!--</div>-->
            <!--</div>-->
            <!--<div class="media-body" ng-click="un.notification.patientId ? $state.go('app.patient.profile', {patientId : un.notification.patientId}) : null">-->
            <!--<span class="block"-->
            <!--style="font-size: 10px;padding: 0px;font-weight: bolder;" ng-bind-html="utilService.formatName(un.notification.patient, 'FL')">-->
            <!--</span>-->
            <!--<span class="block"-->
            <!--style="font-size: 10px;padding: 0px;" ng-bind-html="un.notification.message"></span>-->
            <!--<span class="block" style="font-size: 10px;padding: 0px;">-->
            <!--<span style="font-size: 10px;padding: 0px;"-->
            <!--ng-class="{'text-success': un.notification.alertType === 0, 'text-warning': un.notification.alertType === 1, 'text-primary': un.notification.alertType === 2}">-->
            <!--<i class="fa fa-money" ng-show="un.notification.alertType == 1"></i>-->
            <!--<i class="fa fa-medkit" ng-show="un.notification.alertType == 2"></i>-->
            <!--<i class="fa fa-users" ng-show="un.notification.alertType != 1 && un.notification.alertType != 2"></i>-->
            <!--</span>-->
            <!--<small class="text-muted"-->
            <!--style="font-size: 10px;padding: 0px;" am-time-ago="un.notification.createdAt">-->
            <!--</small>-->
            <!--</span>-->
            <!--</div>-->
            <!--<div class="media-right text-danger" style="padding: 5px;" ng-click="userNotificationService.markRead(un.id)">-->
            <!--<i class="fa fa-trash" title="Mark as Read"></i>-->
            <!--</div>-->
            <!--</div>-->
            <!--</li>-->
            <!--</ul>-->

            <!--&lt;!&ndash;<div class="panel-footer">&ndash;&gt;-->
            <!--&lt;!&ndash;<a href ng-click="main.settings.rightbarShow = !main.settings.rightbarShow">{{main.settings.rightbarShow ? 'Hide' : 'Show'}} all notifications <i class="fa fa-angle-right" class="pull-right"></i></a>&ndash;&gt;-->
            <!--&lt;!&ndash;</div>&ndash;&gt;-->


            <!--<ul class="dropdown-menu animated littleFadeInRight" role="menu" ng-style="{'padding-right': '20px'}"></ul>-->

            <!--</div>-->

        </li>

        <li class="dropdown nav-profile" uib-dropdown>

            <a href class="dropdown-toggle" id="header-user-dropdown" uib-dropdown-toggle>
                <img profile-photo class="img-circle size-30x30" user-id="{{sessionService.getCurrentUser().id}}">
                <span ng-bind-html="utilService.formatName(sessionService.getCurrentUser(), 'FL')">
                    <i class="fa fa-angle-down"></i>
                </span>
            </a>

            <ul class="pull-right dropdown-menu animated littleFadeInRight" role="menu">

                <li ng-if="authService.isSuperAdmin() || authService.isMultiUser()">
                    <a href ng-click="changeCompany(true)" id="change-company-link">
                        <i class="fa fa-building"></i>
                        <span ng-bind-html="' '+sessionService.getCurrentCompany().name"></span>
                    </a>
                </li>
                <li>
                    <a ui-sref="app.user">
                        <i class="fa fa-user"></i>
                        Profile
                    </a>
                </li>
                <li>
                    <a ui-sref="app.tasks.all">
                        <i class="fa fa-check-square"></i>
                        Tasks (deprecated)
                    </a>
                </li>
                <li>
                    <a href ng-click="utilService.openNymbl2Link('/productivity/tasks')">
                        <i class="fa fa-certificate"></i>
                        Tasks
<!--                        <em>New!</em>-->
                    </a>
                </li>
                <li>
                    <a href ng-click="sendMessage()">
                        <i class="fa fa-envelope"></i>
                        Send Message
                    </a>
                </li>
                <li>
                    <a>
                        <i class="fa fa-book"></i>
                        <span ng-bind-html="authService.getAppBuildVersion()"></span>
                    </a>
                </li>
                <li class="divider"></li>
                <li>
                    <a href ng-click="authService.logout()">
                        <i class="fa fa-sign-out"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </li>
        <!--<li class="toggle-right-sidebar">-->
        <!--<a href ng-click="main.settings.rightbarShow = !main.settings.rightbarShow">-->
        <!--<i class="fa fa-comments"></i>-->
        <!--</a>-->
        <!--</li>-->
    </ul>

    <!-- Right-side navigation end -->
</header>
<ng-include src="'views/tmpl/_view_announcement_modal.html'"></ng-include>
<script type="text/ng-template" id="change_company_modal_form.html">
    <div class="modal-content" modal-movable>
        <div class="modal-header">
            <h4 class="modal-title">Change Company</h4>
        </div>
        <div class="modal-body">
            <div class="row">
                <form id="change-company-modal-form" class="col-sm-12 ajax-crud-form">
                    <div id="change-password-alert-container" class="alert" style="display: none;"></div>
                    <div class="row">
                        <div class="col-sm-8 col-sm-offset-2 form-group">
                            <label for="company">Company</label>
                            <select chosen disable-search="{{isMobile}}" id="company" name="company" class="form-control input-sm chosen-select"
                                    ng-model="company"
                                    disable-search="{{isMobile}}"
                                    ng-options="c as c.name + ' - [' + c.key + ']' disable when c.id == authService.getCompanyId() for c in companies | orderBy: 'name'">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            <button id="change-company-save" type="button" class="btn btn-primary btn-rounded btn-sm" ng-click="save()">
                Change Company
            </button>
            <button id="change-company-cancel" type="button" class="btn btn-default btn-rounded btn-sm" ng-if="canCancel"
                    ng-click="cancel()">Cancel
            </button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="select_branch_modal_form.html">
    <div class="modal-content" modal-movable>
        <div class="modal-header">
            <h4 class="modal-title">Please Select a Branch</h4>
        </div>
        <div class="modal-body">
            <div class="row">
                <form id="select-branch-modal-form" class="col-sm-12 ajax-crud-form">
                    <div id="branch-alert-container" class="alert" style="display: none;"></div>
                    <div class="row">
                        <div class="col-sm-8 col-sm-offset-2 form-group">
                            <label for="initialBranch">Branch</label>
                            <select chosen disable-search="{{isMobile}}" id="initialBranch" name="initialBranch" class="form-control input-sm chosen-select"
                                    ng-model="branch"
                                    disable-search="{{isMobile}}"
                                    ng-options="b as b.name for b in branchService.userBranches | orderBy: 'name'">
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-primary btn-rounded btn-sm" ng-click="selectInitialBranch()">Select Branch</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="system_slowness.html">
    <!--    <div class="modal-content">-->
    <!--        <div class="modal-header">-->
    <!--            <h4 class="modal-title">System slowness issues</h4>-->
    <!--        </div>-->
    <!--        <div class="modal-body">-->
    <!--            <div class="row m-10">-->
    <!--                <p>-->
    <!--                    Hi Nymblers,<br>-->
    <!--                    We are aware of the slow speeds with Nymbl today and we want to apologize for the inconvenience you-->
    <!--                    are-->
    <!--                    experiencing at this time. Our team is working diligently to get this issue resolved as soon as-->
    <!--                    possible. There are a couple of things they are currently working through that should resolve the-->
    <!--                    issue.-->
    <!--                    We will-->
    <!--                    continue to keep you all updated throughout the process and will be on standby to help with-->
    <!--                    anything.-->
    <!--                </p>-->
    <!--                <p>-->
    <!--                    Please let us know if you all need anything at the moment, thanks!-->
    <!--                </p>-->
    <!--                <p>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--        <div class="modal-footer">-->
    <!--            <button type="button" class="btn btn-default btn-rounded btn-sm" ng-click="$dismiss()">Close-->
    <!--            </button>-->
    <!--        </div>-->
</script>

<!--<script type="text/ng-template" id="system_maintenance.html">-->
<!--    <div class="modal-content">-->
<!--        <div class="modal-header">-->
<!--            <h4 class="modal-title">April Webinars and Office Hours Schedule!</h4>-->
<!--        </div>-->
<!--        <div class="modal-body">-->
<!--            <div class="row m-10">-->
<!--                <p>Hi Nymblers,</p>-->
<!--                <p>Below is our schedule for Nymbl's webinars and office hours this month:</p>-->
<!--                <ul>-->
<!--                    <li class="mb-10">-->
<!--                        <strong>Wednesday, April 14th 12:00pm-12:30pm EST - Nymbl Education Webinar</strong>-->
<!--                        <p>Hosted by: Elizabeth Farmer</p>-->
<!--                        <p>Topic: Notes Refresher- Notes, Note Templates, Advanced Forms, Care Extender Note Approval, Missing Notes</p>-->
<!--                        <p><a href="http://attend.zoho.com/jrul">Register Here</a></p>-->
<!--                    </li>-->
<!--                    <li class="mb-10">-->
<!--                        <strong>Wednesday, April 21st 12:00pm-1:00pm EST- Nymbl Office Hours</strong><br>-->
<!--                        <p>Hosted by: Josh Black</p>-->
<!--                        <p>This is an open session where you all can join and ask any questions you have about the-->
<!--                            software</p>-->
<!--                        <p><a href="http://attend.zoho.com/gobw">Join Here</a></p>-->
<!--                    </li>-->
<!--                    <li class="mb-10">-->
<!--                        <strong>Wednesday, April 28th 12:00pm-12:30pm EST- O&P Educational Webinar</strong><br>-->
<!--                        <p>Hosted by: Michelle Wullstein</p>-->
<!--                        <p>Topic: Competitive Bidding</p>-->
<!--                        <p><a href="http://attend.zoho.com/nivl">Register Here</a></p>-->
<!--                    </li>-->
<!--                </ul>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="modal-footer">-->
<!--            <button type="button" class="btn btn-default btn-rounded btn-sm" ng-click="$dismiss()">Close</button>-->
<!--        </div>-->
<!--    </div>-->
<!--</script>-->

<script type="text/ng-template" id="send_message_modal_form.html">
    <div class="modal-content" modal-movable>
        <div class="modal-header">
            <h4 class="modal-title"> {{ viewOnly? 'View' : 'Send' }} Message</h4>
        </div>
    </div>
    <div class="modal-body" ng-if="!loading">
        <div class="row">
            <div id="send-message-modal-form" class="col-sm-12 ajax-crud-form">
                <div id="send-message-alert-container" class="alert" style="display: none;"></div>
                <div class="row" ng-if="!viewOnly">
                    <button ng-click="roleSelect()" class="btn btn-sm pull-right">Select role</button>
                </div>
                <div ng-hide="sendToRole || viewOnly" class="col-xs-12 form-group">
                    <label>To:</label>
                    <ui-select multiple ng-model="message.userIds" ng-disabled="disabled">
                        <ui-select-match placeholder="Select users, an empty list will send to everyone...">
                            {{utilService.formatName($item, 'LFMC')}}
                        </ui-select-match>
                        <ui-select-choices repeat="user.id as user in users | filter:{firstAndLastName: $select.search}"
                                           ui-disable-choice="isActive(user.id)">
                            <div ng-bind-html="utilService.formatName(user, 'LFMC')"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div ng-hide="!sendToRole || viewOnly" class="col-xs-12 form-group">
                    <label>To:</label>
                    <select chosen
                            class="form-control input-sm chosen-select"
                            ng-model="message.roleId"
                            ng-disabled="editing"
                            disable-search="{{isMobile}}"
                            ng-options="role.id as role.name for role in roles">
                        <option value=""></option>
                    </select>
                </div>
                <div class="col-xs-12 form-group" ng-hide="viewOnly">
                    <label>Tag Patient:</label>
                    <div>
                        <input ng-if='!patient' type="text" size="40" class="form-control"
                               placeholder="Search Patient by Name, Id, SSN, or DOB (MMDDYYYY)"
                               uib-typeahead="selected as utilService.formatNameWithDOB(patient) for patient in getPatients($viewValue)"
                               ng-model="selected"
                               typeahead-on-select="selectPatient($item, $model, $label); selected=$item"
                               typeahead-min-length="1"
                               typeahead-wait-ms="500" typeahead-template-url="patientWithDOBSearchTemplate.html"
                        />
                        <span ng-if="patient" name="patientId" type="text" size="20"
                              class="form-control chosen-select text-uppercase">{{patient.firstName + ' ' + patient.lastName}}</span>

                    </div>
                    <!--<div>-->
                    <!--{{'#' + patient.id + ' ' + patient.firstName + ' ' + patient.lastName }}-->
                    <!--</div>-->
                </div>
                <div class="col-xs-12 form-group" ng-hide="viewOnly">
                    <label>Message:</label>
                    <textarea style="width:100%; height:200px" class="form-control"
                              ng-model="message.message"></textarea>
                </div>
            </div>
        </div>
        <div style="padding-left: 15px;" ng-repeat="dto in messageThread">
            <hr/>
            <div class="row">
                <div class="col-xs-8">
                    <div><span style="font-weight: bold;">To: </span> {{ dto.recipientNames }}</div>
                </div>
                <div class="col-xs-4">
                    <span style="font-weight: bold;">{{ moment(dto.createdAt).format('MMMM Do, YYYY') }}</span>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-8">
                    <div><span style="font-weight: bold;">From: </span>{{ dto.senderName }}</div>
                </div>
                <div class="col-xs-4 notification-alert-type"
                     ng-class="{'text-success': dto.alertType === 0,
                                'text-warning': dto.alertType === 1,
                                'text-primary': dto.alertType === 2}">
                    <span ng-show="dto.alertType == 1">Billing</span>
                    <span ng-show="dto.alertType == 2">Clerical</span>
                    <span ng-show="dto.alertType != 1 && dto.alertType != 2">General</span>
                </div>
            </div>
            <div class="row" ng-if="dto.patientId">
                <div class="col-xs-8">
                    <div><span style="font-weight: bold;">Patient: </span>{{ dto.patientName }}</div>
                </div>
            </div>
            <div class="row mt-15">
                <div class="col-xs-12 form-group">
                    <div ng-bind-html="sce.trustAsHtml(dto.notificationMessageContents)"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-body" ng-if="loading">
        <div class="row text-center">
            <i class="fa fa-spinner fa-spin fa-5x" ng-style="{ 'color': '#16A085' }"></i>
        </div>
    </div>
    <div class="modal-footer" ng-if="!loading">
        <button type="button" class="btn btn-default btn-rounded btn-sm" ng-if="!viewOnly" ng-click="send()">Send
        </button>
        <button type="button" class="btn btn-default btn-rounded btn-sm" ng-if="viewOnly" ng-click="reply(false)">
            Reply
        </button>
        <button type="button" class="btn btn-default btn-rounded btn-sm" ng-if="viewOnly" ng-click="reply(true)">Reply
            All
        </button>
        <button type="button" class="btn btn-default btn-rounded btn-sm" ng-click="cancel()">Cancel</button>
    </div>
</script>

<ng-include src="'views/tmpl/patient/_search_result_templates.html'"></ng-include>
