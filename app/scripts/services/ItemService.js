app.service('ItemService', ItemService);
ItemService.$inject = ['$rootScope', '$uibModal', '$filter', '$moment', '$q', 'CacheFactory', 'NoteTypesFactory', 'NoteFactory',
	'TemplateFactory', 'UserService', 'PrescriptionService', 'PatientService', 'AppointmentService', 'UtilService',
	'EvaluationFormFactory', 'SystemSettingFactory', 'NoteChildTypesFactory', 'DiffModalService', 'ItemFactory', 'ItemPhysicalFactory'];

function ItemService($rootScope, $uibModal, $filter, $moment, $q, CacheFactory, NoteTypesFactory, NoteFactory, TemplateFactory,
                     UserService, PrescriptionService, PatientService, AppointmentService, UtilService,
                     EvaluationFormFactory, SystemSettingFactory, NoteChildTypesFactory, DiffModalService, ItemFactory, ItemPhysicalFactory) {

	this.createItemsPhysical = function (itemPhysical, quantity) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.createItems({quantity: quantity}, itemPhysical).$promise.then(function (response) {
						if (response.error) {
							confirm(response.error);
						}
						return response.itemCount;
					}, function (error) {
						confirm(error.error);
						return 0;
					})
				);
			});
		});
	};

	this.deleteItem = function (itemId) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemFactory.deleteItem({itemId: itemId }).$promise.then(function (response) {
						if (response.success) {
							return "OK";
						} else {
							return response.message;
						}
					}, function (error) {
						return error.data.message;
					})
				);
			}, 1000);
		});
	};

	this.deleteItemsPhysical = function (branchId, itemId, purchaseOrderItemId, quantity, reason) {
		var params = {
			branchId: branchId,
			itemId: itemId,
			purchaseOrderItemId: purchaseOrderItemId,
			quantity: quantity,
			reason: reason
		};
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.deleteItems(params).$promise.then(function (response) {
						if (response.error) {
							confirm(response.error);
						}
						return response.itemCount;
					}, function (error) {
						confirm(error.error);
						return 0;
					})
				);
			});
		});
	};

	var displayError = function (error) {
		var errorMessage = "Changes were not saved!";
		if (error) {
			console.error(JSON.stringify(error));
			if (error.data && error.data.message) {
				if (error.data.diffs) {
					DiffModalService.popModal(error.data.diffs, error.data.message, $scope.activeRecord.id, "ItemByManufacturer");
					UtilService.displayAlert('danger', errorMessage, '');
				} else {
					confirm(errorMessage + '\n' + error.data.message);
				}
			} else {
				confirm(errorMessage + ' Please see console for details.');
			}
		} else {
			confirm(errorMessage + ' There is no additional information.');
		}
	};

	this.findItemByPartNumberAndVendor = function(vendorId, partNumber) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemFactory.findItemByPartNumberAndVendor({"vendorId": vendorId}, partNumber).$promise.then(function (response) {
						return response;
					}, function (s) {
						console.error(s);
						return null;
					})
				);
			}, 1000);
		});
	};

	this.findItemPhysicalByPurchaseOrderId = function (purchaseOrderId) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.findByPurchaseOrderId({purchaseOrderId: purchaseOrderId}).$promise.then(function (response) {
						return response;
					}, function (error) {
						if (error && error.data && error.data.message) console.log(error.data.message);
						return [];
					})
				);
			}, 1000);
		}, function (err) {
			console.error(JSON.stringify(err));
		});
	};

	this.findItemPhysicalByBranchIdAndItemId = function (branchId, itemId, quantity, status) {
		var params = {
			branchId: branchId,
			itemId: itemId,
			quantity: quantity,
			status: status
		};
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.findByBranchIdAndItemId(params).$promise.then(function (response) {
						return response;
					}, function (error) {
						if (error && error.data && error.data.message) console.log(error.data.message);
						return [];
					})
				);
			}, 1000);
		});
	};

	this.getItem = function(itemId) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemFactory.get({id: itemId}).$promise.then(function (item) {
						return item;
					})
				);
			});
		});
	};

	this.getItemPhysicalCountsByPoiId = function (purchaseOrderId) {
		var params = {
			purchaseOrderId: purchaseOrderId
		};
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.getInventoryPoItemCountsByPoiId(params).$promise.then(function (response) {
						return response;
					}, function (error) {
						if (error && error.data && error.data.message) console.log(error.data.message);
						return [];
					})
				);
			}, 1000);
		});
	};

	this.getItems = function (params) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve(
					ItemFactory.getItems(params).$promise.then(function (response) {
						return response;
					}, function (error) {
						console.log(JSON.stringify(error));
						return null;
					})
				);
			}, 1000);
		});
	};

	this.getItemsByManufacturer = function(keywords, manufacturerId) {
		var params = {
			keywords: keywords,
			manufacturerId: manufacturerId
		};
		return new Promise(function (resolve) {
			if (!keywords) return [];
			setTimeout(function () {
				resolve (
					ItemFactory.getItemsByManufacturer(params).$promise.then(function (response) {
						return response;
					}, function (error) {
						console.log(error);
						return [];
					})
				);
			}, 1000);
		});
	};

	this.getItemsPhysical = function (itemId, allStatuses, blankSerial) {
		var params = {
			itemId: itemId,
			allStatuses: allStatuses,
			blankSerial: blankSerial
		};
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.getItems(params).$promise.then(function (response) {
						return response;
					}, function (error) {
						if (error && error.data && error.data.message) console.log(error.data.message);
						return [];
					})
			  );
			}, 1000);
		});
	};

	this.openItemPhysicalModal = function (caller, item, itemPhysical, branchId) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/purchasing/items/_item_physical_modal.html',
			controller: 'ItemPhysicalModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				branchId: function () {
					return branchId;
				},
				caller: function() {
					return caller;
				},
				item: function () {
					return item;
				},
				itemPhysical: function() {
					return itemPhysical;
				}
			}
		}).closed.then(function () {
			$rootScope.$emit('itemPhysicalModalClosed', null);
		});
	};

	this.saveItem = function (item) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemFactory.save(item).$promise.then(function (saved) {
						return saved;
					}, function (error) {
						displayError(error);
						return null;
					})
				);
			}, 1000);
		});
	};

	this.saveItemByManufacturer = function (itemByManufacturer) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (
					ItemFactory.saveItemByManufacturer(itemByManufacturer).$promise.then(function (response) {
						return response;
					}, function (error) {
						displayError(error);
						return null;
					})
				);
			}, 1000);
		});
	};

	this.saveItemPhysical = function (itemPhysical) {
		return new Promise (function (resolve) {
			setTimeout(function () {
				resolve (
					ItemPhysicalFactory.save(itemPhysical).$promise.then(function (response) {
						return response;
					}, function (error) {
						if (error && error.data && error.data.message) {
							console.log(error.data.message);
							if (error.data.diffs != null) {
								DiffModalService.popModal(error.data.diffs, error.data.message, $scope.activeRecord.id, "Item");
							} else {
								confirm('Changes were not saved! Please see console for details.');
								console.error(JSON.stringify(error));
								return null;
							}
						}
					})
				);
			}, 1000);
		});
	};

	this.saveItemPhysicalAll = function (items) {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve(
					ItemPhysicalFactory.saveAll(items).$promise.then(function (response) {
						return response;
					}, function (error) {
						return error;
					})
				);
			}, 1000);
		});
	};

	this.searchRental = function() {
		return new Promise(function (resolve) {
			setTimeout(function () {
				resolve (

				);
			});
		});
	};

	this.updateItemPhysicalStatus = function (prescriptionId, prescriptionLCodeId, itemStatus, reason, codedReasons) {
		return new Promise(function(resolve) {
			setTimeout(function (){
				resolve (
					ItemPhysicalFactory.findByPrescription({prescriptionId: prescriptionId, prescriptionLCodeId: prescriptionLCodeId, stripDown: false}).$promise.then(function (response) {
						var physicalItems = [];
						angular.forEach(response, function (ip) {
							if (ip.status != itemStatus) {
								if (!ip.reason || codedReasons.includes(ip.reason)) {
									ip.reason = reason;
								}
								ip.status = itemStatus;
								physicalItems.push(ip);
							}
						});
						return physicalItems;
					})
				);
			}, 1000);
		}).then(function (items) {
			return new Promise(function (resolve) {
				setTimeout(function () {
					resolve(
						ItemPhysicalFactory.saveAll(items).$promise.then(function (response) {
							var statusChanged = response.length > 0;
							return statusChanged;
						})
					);
				}, 1000);
			});
		});
	};
}