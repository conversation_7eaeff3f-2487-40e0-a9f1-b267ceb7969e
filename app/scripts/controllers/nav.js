'use strict';
app.controller('NavCtrl', NavCtrl);
NavCtrl.$inject = ['$scope', '$moment', '$state', 'UserService', 'UtilService', 'TabService', 'PrescriptionService', 'ClaimService', '$window', '$cookies', 'SystemSettingFactory'];

function NavCtrl($scope, $moment, $state, UserService, UtilService, TabService, PrescriptionService, ClaimService, $window, $cookies, SystemSettingFactory) {

	var isInitialTreeLoad = true;
	$scope.hasPermissionDirect = {};

	$scope.$on("readyToLoadLeftNav", function () {
		if(isInitialTreeLoad) {
			$scope.userService = UserService;
			$scope.utilService = UtilService;
			$scope.tabService = TabService;
			$scope.zendeskWidgetOpen = false;
			$scope.status = {
				isFirstOpen: true,
				isSecondOpen: false,
				isThirdOpen: false,
				oneAtATime: true
			};

			SystemSettingFactory.findBySectionAndField({
				section: "billing",
				field: "stripe_pay_on"
			}).$promise.then(function (response) {
				$scope.directPayOn = response.value === 'Y';
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "ai_notes_on"
			}).$promise.then(function (response) {
				$scope.aiNotesOn = response.value === 'Y';
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "lmn_on"
			}).$promise.then(function (response) {
				$scope.lmnOn = response.value === 'Y';
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "hide_device_type"
			}).$promise.then(function (response) {
				$scope.crtActive = response.value === 'Y';
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "enable_loaners"
			}).$promise.then(function (response) {
				$scope.enableLoaners = response.value === 'Y';
			});

			SystemSettingFactory.findBySectionAndField({
				section: "general",
				field: "enable_batch_sign"
			}).$promise.then(function (response) {
				$scope.enableBatchSign = response.value === 'Y';
			});

			$scope.zendeskWidgetOpen = false;
			$scope.newPatch = false;
			$scope.hasV2RxSummaryAccess = UserService.features.hasV2RxSummaryAccess;

			isInitialTreeLoad = false;
			$scope.hasPermissionDirect = UserService.permissionsObj;
			$scope.$apply();
		}
	});


	$scope.toggleZendeskWidget = function () {
		if ($scope.zendeskWidgetOpen == false) {
			zE('webWidget', 'show');
		} else {
			zE('webWidget', 'hide');
		}
		$scope.zendeskWidgetOpen = !$scope.zendeskWidgetOpen;
	};

	$scope.openCRMLink = function () {
		var url = $('#nymblV2Url').attr("value");
		$window.open(url + "crm/activities?token=" + $cookies.get("X-Auth-Token"), '_blank');
	}

	$scope.openOutstandingBalancesLink = function () {
		var url = $('#nymblV2Url').attr("value");
		$window.open(url + "reports/financial/balances/outstanding-balances?token=" + $cookies.get("X-Auth-Token"), '_blank');
	}


	$scope.openNewPatientLink = function () {
		var url = $('#nymblV2Url').attr("value");
		$window.open(url + "patients/patient/new?token=" + $cookies.get("X-Auth-Token"), '_blank');
	}

	$scope.openRxSummaryLink = function () {
		var url = $('#nymblV2Url').attr("value");
		$window.open(url + "patients/summary?token=" + $cookies.get("X-Auth-Token"), '_blank');
	}

	$scope.openNymbl2Link = UtilService.openNymbl2Link;


	$scope.patientProfileLink = function (patientId) {

		//unnecessary at this point, but hold onto it
		//if(PrescriptionService.loading || ClaimService.loading || PatientService.loading){
		//  return null;
		//} else {
		$state.go('app.patient.profile', {patientId: patientId});
		//}
	}
}
